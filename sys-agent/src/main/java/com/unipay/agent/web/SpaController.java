package com.unipay.agent.web;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * SPA 前端路由支持控制器
 * 处理所有非 API 请求，转发到 index.html 让前端路由接管
 *
 * <AUTHOR>
 * @date 2025/9/15
 */
@Controller
public class SpaController {

    /**
     * 处理所有前端路由请求，转发到 index.html
     * 排除 API 接口、静态资源等
     */
    @RequestMapping(value = {
        "/main/**", "/users/**", "/roles/**", "/mch/**", "/mchApp/**",
        "/agent/**", "/payOrder/**", "/refundOrder/**", "/transferOrder/**",
        "/mchNotify/**", "/payways/**", "/profitRecord/**", "/current/**",
        "/login", "/dashboard/**"
    })
    public String forward() {
        return "forward:/index.html";
    }
}