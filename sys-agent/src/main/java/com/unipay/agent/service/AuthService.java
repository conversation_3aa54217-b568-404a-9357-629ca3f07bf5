
package com.unipay.agent.service;

import cn.hutool.core.util.IdUtil;
import com.unipay.core.cache.ITokenService;
import com.unipay.core.cache.RedisUtil;
import com.unipay.core.constants.CS;
import com.unipay.core.entity.AgentInfo;
import com.unipay.core.entity.SysUser;
import com.unipay.core.exception.BizException;
import com.unipay.core.exception.JeepayAuthenticationException;
import com.unipay.core.jwt.JWTPayload;
import com.unipay.core.jwt.JWTUtils;
import com.unipay.core.model.security.JeeUserDetails;
import com.unipay.agent.config.SystemYmlConfig;
import com.unipay.service.impl.AgentInfoService;
import com.unipay.service.impl.SysRoleEntRelaService;
import com.unipay.service.impl.SysRoleService;
import com.unipay.service.impl.SysUserService;
import com.unipay.service.impl.SysUserAuthService;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import com.unipay.service.mapper.SysEntitlementMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 认证Service
 *
 * @modify zhuxiao 
 * @date 2021-04-27 15:50
 */
@Slf4j
@Service
public class AuthService {

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired private SysUserService sysUserService;
    @Autowired private SysRoleService sysRoleService;
    @Autowired private SysRoleEntRelaService sysRoleEntRelaService;
    @Autowired private AgentInfoService agentInfoService;
    @Autowired private SysEntitlementMapper sysEntitlementMapper;
    @Autowired private SystemYmlConfig systemYmlConfig;
    @Autowired private SysUserAuthService sysUserAuthService;

    /**
     * 认证
     * **/
    public String auth(String username, String password){

        //1. 生成spring-security usernamePassword类型对象
        UsernamePasswordAuthenticationToken upToken = new UsernamePasswordAuthenticationToken(username, password);

        //spring-security 自动认证过程；
        // 1. 进入 JeeUserDetailsServiceImpl.loadUserByUsername 获取用户基本信息；
        //2. SS根据UserDetails接口验证是否用户可用；
        //3. 最后返回loadUserByUsername 封装的对象信息；
        Authentication authentication = null;
        try {
            authentication = authenticationManager.authenticate(upToken);
        } catch (JeepayAuthenticationException jex) {
            throw jex.getBizException() == null ? new BizException(jex.getMessage()) : jex.getBizException();
        } catch (BadCredentialsException e) {
            throw new BizException("用户名/密码错误！");
        } catch (AuthenticationException e) {
            log.error("AuthenticationException:", e);
            throw new BizException("认证服务出现异常， 请重试或联系系统管理员！");
        }
        JeeUserDetails jeeUserDetails = (JeeUserDetails) authentication.getPrincipal();

        //验证通过后 再查询用户角色和权限信息集合

        SysUser sysUser = jeeUserDetails.getSysUser();

        //非超级管理员 && 不包含左侧菜单 进行错误提示
        if(sysUser.getIsAdmin() != CS.YES && sysEntitlementMapper.userHasLeftMenu(sysUser.getSysUserId(), CS.SYS_TYPE.AGENT) <= 0){
            throw new BizException("当前用户未分配任何菜单权限，请联系管理员进行分配后再登录！");
        }

        // 查询当前用户的代理商信息
        AgentInfo agentInfo = agentInfoService.getByAgentNo(sysUser.getBelongInfoId());
        if (agentInfo != null) {
            // 判断当前代理商状态是否可用
            if (agentInfo.getState() == CS.NO) {
                throw new BizException("当前代理商状态不可用！");
            }
        }
        // 放置权限集合
        jeeUserDetails.setAuthorities(getUserAuthority(sysUser));

        //生成token
        String cacheKey = CS.getCacheKeyToken(sysUser.getSysUserId(), IdUtil.fastUUID());

        //生成iToken 并放置到缓存
        ITokenService.processTokenCache(jeeUserDetails, cacheKey); //处理token 缓存信息

        //将信息放置到Spring-security context中
        UsernamePasswordAuthenticationToken authenticationRest = new UsernamePasswordAuthenticationToken(jeeUserDetails, null, jeeUserDetails.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authenticationRest);

        //返回JWTToken
        return JWTUtils.generateToken(new JWTPayload(jeeUserDetails), systemYmlConfig.getJwtSecret());
    }

    /** 根据用户ID 更新缓存中的权限集合， 使得分配实时生效  **/
    public void refAuthentication(List<Long> sysUserIdList){

        if(sysUserIdList == null || sysUserIdList.isEmpty()){
            return ;
        }

        Map<Long, SysUser> sysUserMap = new HashMap<>();

        // 查询 sysUserId 和 state
        sysUserService.list(
                SysUser.gw()
                        .select(SysUser::getSysUserId, SysUser::getState)
                        .in(SysUser::getSysUserId, sysUserIdList)
        ).stream().forEach(item -> sysUserMap.put(item.getSysUserId(), item));

        for (Long sysUserId : sysUserIdList) {

            Collection<String> cacheKeyList = RedisUtil.keys(CS.getCacheKeyToken(sysUserId, "*"));
            if(cacheKeyList == null || cacheKeyList.isEmpty()){
                continue;
            }

            for (String cacheKey : cacheKeyList) {

                //用户不存在 || 已禁用 需要删除Redis
                if(sysUserMap.get(sysUserId) == null || sysUserMap.get(sysUserId).getState() == CS.PUB_DISABLE){
                    RedisUtil.del(cacheKey);
                    continue;
                }

                JeeUserDetails jwtBaseUser = RedisUtil.getObject(cacheKey, JeeUserDetails.class);
                if(jwtBaseUser == null){
                    continue;
                }

                // 重新放置sysUser对象
                jwtBaseUser.setSysUser(sysUserService.getById(sysUserId));

                //查询放置权限数据
                jwtBaseUser.setAuthorities(getUserAuthority(jwtBaseUser.getSysUser()));

                //保存token  失效时间不变
                RedisUtil.set(cacheKey, jwtBaseUser);
            }
        }

    }

    /** 根据用户ID 删除用户缓存信息  **/
    public void delAuthentication(List<Long> sysUserIdList){
        if(sysUserIdList == null || sysUserIdList.isEmpty()){
            return ;
        }
        for (Long sysUserId : sysUserIdList) {
            Collection<String> cacheKeyList = RedisUtil.keys(CS.getCacheKeyToken(sysUserId, "*"));
            if(cacheKeyList == null || cacheKeyList.isEmpty()){
                continue;
            }
            for (String cacheKey : cacheKeyList) {
                RedisUtil.del(cacheKey);
            }
        }
    }

    public List<SimpleGrantedAuthority> getUserAuthority(SysUser sysUser){

        //用户拥有的角色集合  需要以ROLE_ 开头,  用户拥有的权限集合
        List<String> roleList = sysRoleService.findListByUser(sysUser.getSysUserId());
        List<String> entList = sysRoleEntRelaService.selectEntIdsByUserId(sysUser.getSysUserId(), sysUser.getIsAdmin(), sysUser.getSysType());

        List<SimpleGrantedAuthority> grantedAuthorities = new LinkedList<>();
        roleList.stream().forEach(role -> grantedAuthorities.add(new SimpleGrantedAuthority(role)));
        entList.stream().forEach(ent -> grantedAuthorities.add(new SimpleGrantedAuthority(ent)));
        return grantedAuthorities;
    }

    /**
     * 代理商注册
     */
    @Transactional(rollbackFor = Exception.class)
    public void registerAgent(String agentName, String agentShortName, String contactName,
                             String contactTel, String loginUsername, String password) {

        // 检查登录用户名是否已存在
        if (sysUserService.count(SysUser.gw()
                .eq(SysUser::getSysType, CS.SYS_TYPE.AGENT)
                .eq(SysUser::getLoginUsername, loginUsername)) > 0) {
            throw new BizException("登录用户名已存在！");
        }

        // 检查手机号是否已存在
        if (sysUserService.count(SysUser.gw()
                .eq(SysUser::getSysType, CS.SYS_TYPE.AGENT)
                .eq(SysUser::getTelphone, contactTel)) > 0) {
            throw new BizException("该手机号已被注册！");
        }

        // 创建代理商信息
        AgentInfo agentInfo = new AgentInfo();
        agentInfo.setAgentName(agentName);
        agentInfo.setAgentShortName(agentShortName);
        agentInfo.setContactName(contactName);
        agentInfo.setContactTel(contactTel);
        agentInfo.setAgentType(AgentInfo.TYPE_LEVEL_1); // 默认为一级代理商
        agentInfo.setAgentLevel((byte) 1); // 默认层级为1
        agentInfo.setState(AgentInfo.STATE_NORMAL); // 默认启用状态
        agentInfo.setCanDevelopAgent((byte) 0); // 默认不允许发展下级代理商
        agentInfo.setCanDevelopMch((byte) 1); // 默认允许发展商户
        agentInfo.setCreatedAt(new Date());
        agentInfo.setUpdatedAt(new Date());
        agentInfo.setCreatedBy("系统注册");

        // 调用代理商服务创建代理商和用户
        agentInfoService.createAgentWithUser(agentInfo, password);
    }


}
