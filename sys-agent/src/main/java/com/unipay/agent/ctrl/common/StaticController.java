package com.unipay.agent.ctrl.common;

import com.unipay.agent.ctrl.CommonCtrl;
import com.unipay.components.oss.config.OssYmlConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;

@Controller
public class StaticController extends CommonCtrl {

    @Autowired private OssYmlConfig ossYmlConfig;

    /** 图片预览 **/
    @GetMapping("/api/anon/localOssFiles/*/*.*")
    public ResponseEntity<?> imgView() {

        try {

            //查找图片文件
            File imgFile = new File(ossYmlConfig.getOss().getFilePublicPath() + File.separator + request.getRequestURI().substring(24));
            if(!imgFile.isFile() || !imgFile.exists()) {
                return new ResponseEntity<>(HttpStatus.NOT_FOUND);
            }

            //输出文件流（图片格式）
            HttpHeaders httpHeaders = new HttpHeaders();
            // 根据文件扩展名设置正确的Content-Type
            String fileName = imgFile.getName().toLowerCase();
            if (fileName.endsWith(".png")) {
                httpHeaders.setContentType(org.springframework.http.MediaType.IMAGE_PNG);
            } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
                httpHeaders.setContentType(org.springframework.http.MediaType.IMAGE_JPEG);
            } else if (fileName.endsWith(".gif")) {
                httpHeaders.setContentType(org.springframework.http.MediaType.IMAGE_GIF);
            } else {
                httpHeaders.setContentType(org.springframework.http.MediaType.APPLICATION_OCTET_STREAM);
            }
            
            InputStream inputStream = new FileInputStream(imgFile);
            InputStreamResource resource = new InputStreamResource(inputStream);
            return ResponseEntity.ok()
                    .headers(httpHeaders)
                    .contentLength(imgFile.length())
                    .body(resource);

        } catch (FileNotFoundException e) {
            logger.error("static file error", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


}
