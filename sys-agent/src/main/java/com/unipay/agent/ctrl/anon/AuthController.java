
package com.unipay.agent.ctrl.anon;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.unipay.core.aop.MethodLog;
import com.unipay.core.cache.RedisUtil;
import com.unipay.core.constants.CS;
import com.unipay.core.exception.BizException;
import com.unipay.core.model.ApiRes;
import com.unipay.agent.ctrl.CommonCtrl;
import com.unipay.agent.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录鉴权
 *
 * <AUTHOR> 
 * @date 2021-04-27 15:50
 */
@Tag(name = "认证模块")
@RestController
@RequestMapping("/api/anon/auth")
public class AuthController extends CommonCtrl {

	@Autowired private AuthService authService;

	/** 用户信息认证 获取iToken  **/
	@Operation(summary = "登录认证")
	@Parameters({
			@Parameter(name = "ia", description = "用户名 i account, 需要base64处理", required = true),
			@Parameter(name = "ip", description = "密码 i passport,  需要base64处理", required = true),
			@Parameter(name = "vc", description = "证码 vercode,  需要base64处理", required = true),
			@Parameter(name = "vt", description = "验证码token, vercode token ,  需要base64处理", required = true)
	})
	@RequestMapping(value = "/validate", method = RequestMethod.POST)
	@MethodLog(remark = "登录认证")
	public ApiRes validate() throws BizException {

		String account = Base64.decodeStr(getValStringRequired("ia"));  //用户名 i account, 已做base64处理
		String ipassport = Base64.decodeStr(getValStringRequired("ip"));	//密码 i passport,  已做base64处理
        String vercode = Base64.decodeStr(getValStringRequired("vc"));	//验证码 vercode,  已做base64处理
        String vercodeToken = Base64.decodeStr(getValStringRequired("vt"));	//验证码token, vercode token ,  已做base64处理

        String cacheCode = RedisUtil.getString(CS.getCacheKeyImgCode(vercodeToken));
        if(StringUtils.isEmpty(cacheCode) || !cacheCode.equalsIgnoreCase(vercode)){
            throw new BizException("验证码有误！");
        }

		// 返回前端 accessToken
		String accessToken = authService.auth(account, ipassport);

        // 删除图形验证码缓存数据
        RedisUtil.del(CS.getCacheKeyImgCode(vercodeToken));

		return ApiRes.ok4newJson(CS.ACCESS_TOKEN_NAME, accessToken);
	}

	/** 图片验证码  **/
	@Operation(summary = "图片验证码")
	@RequestMapping(value = "/vercode", method = RequestMethod.GET)
	public ApiRes vercode() throws BizException {

		//定义图形验证码的长和宽 // 4位验证码
		LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(137, 40, 4, 80);
        lineCaptcha.createCode(); //生成code

        //redis
		String vercodeToken = UUID.fastUUID().toString();
        RedisUtil.setString(CS.getCacheKeyImgCode(vercodeToken), lineCaptcha.getCode(), CS.VERCODE_CACHE_TIME ); //图片验证码缓存时间: 1分钟

        JSONObject result = new JSONObject();
        result.put("imageBase64Data", lineCaptcha.getImageBase64Data());
        result.put("vercodeToken", vercodeToken);
		result.put("expireTime", CS.VERCODE_CACHE_TIME);

		return ApiRes.ok(result);
	}

	/** 代理商注册  **/
	@Operation(summary = "代理商注册")
	@Parameters({
			@Parameter(name = "agentName", description = "代理商名称", required = true),
			@Parameter(name = "agentShortName", description = "代理商简称", required = true),
			@Parameter(name = "contactName", description = "联系人姓名", required = true),
			@Parameter(name = "contactTel", description = "联系人手机号", required = true),
			@Parameter(name = "loginUsername", description = "登录用户名", required = true),
			@Parameter(name = "password", description = "登录密码", required = true),
			@Parameter(name = "vercode", description = "验证码", required = true),
			@Parameter(name = "vercodeToken", description = "验证码token", required = true)
	})
	@RequestMapping(value = "/register", method = RequestMethod.POST)
	@MethodLog(remark = "代理商注册")
	public ApiRes register() throws BizException {

		String agentName = getValStringRequired("agentName");
		String agentShortName = getValStringRequired("agentShortName");
		String contactName = getValStringRequired("contactName");
		String contactTel = getValStringRequired("contactTel");
		String loginUsername = getValStringRequired("loginUsername");
		String password = getValStringRequired("password");
		String vercode = getValStringRequired("vercode");
		String vercodeToken = getValStringRequired("vercodeToken");

		// 验证码校验
		String cacheCode = RedisUtil.getString(CS.getCacheKeyImgCode(vercodeToken));
		if(StringUtils.isEmpty(cacheCode) || !cacheCode.equalsIgnoreCase(vercode)){
			throw new BizException("验证码有误！");
		}

		// 参数校验
		if (StringUtils.isBlank(agentName) || agentName.length() > 64) {
			throw new BizException("代理商名称不能为空且长度不能超过64个字符！");
		}
		if (StringUtils.isBlank(agentShortName) || agentShortName.length() > 32) {
			throw new BizException("代理商简称不能为空且长度不能超过32个字符！");
		}
		if (StringUtils.isBlank(contactName) || contactName.length() > 32) {
			throw new BizException("联系人姓名不能为空且长度不能超过32个字符！");
		}
		if (StringUtils.isBlank(contactTel) || !contactTel.matches("^1[3-9]\\d{9}$")) {
			throw new BizException("请输入正确的手机号！");
		}
		if (StringUtils.isBlank(loginUsername) || loginUsername.length() < 4 || loginUsername.length() > 32) {
			throw new BizException("登录用户名长度应在4-32个字符之间！");
		}
		if (StringUtils.isBlank(password) || password.length() < 6 || password.length() > 32) {
			throw new BizException("密码长度应在6-32个字符之间！");
		}

		// 调用注册服务
		authService.registerAgent(agentName, agentShortName, contactName, contactTel, loginUsername, password);

		// 删除图形验证码缓存数据
		RedisUtil.del(CS.getCacheKeyImgCode(vercodeToken));

		return ApiRes.ok("注册成功！");
	}

}
