<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器错误 - UniPay</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .error-container {
            text-align: center;
            padding: 2rem;
            max-width: 600px;
        }
        
        .error-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .error-title {
            font-size: 2rem;
            margin-bottom: 1rem;
            opacity: 0.9;
        }
        
        .error-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.8;
            line-height: 1.6;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.9);
            color: #ff6b6b;
        }
        
        .btn-primary:hover {
            background: white;
            color: #ee5a24;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 8s ease-in-out infinite;
        }
        
        .shape-1 { width: 100px; height: 100px; top: 10%; left: 15%; animation-delay: 0s; }
        .shape-2 { width: 80px; height: 80px; top: 70%; left: 85%; animation-delay: 3s; }
        .shape-3 { width: 120px; height: 120px; top: 50%; left: 10%; animation-delay: 6s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-15px) rotate(120deg); }
            66% { transform: translateY(10px) rotate(240deg); }
        }
        
        .status-info {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 4rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .error-container {
                padding: 1rem;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
    </div>
    
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <div class="error-code">5XX</div>
        <h1 class="error-title">服务器错误</h1>
        <p class="error-message">
            服务器遇到了一个错误，无法完成您的请求。<br>
            我们的技术团队已经收到通知，正在努力解决这个问题。
        </p>
        
        <div class="action-buttons">
            <a href="/" class="btn btn-primary">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                    <polyline points="9,22 9,12 15,12 15,22"/>
                </svg>
                返回首页
            </a>
            <button class="btn" onclick="window.location.reload()">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="23,4 23,10 17,10"/>
                    <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"/>
                </svg>
                刷新页面
            </button>
        </div>
        
        <div class="status-info">
            <p><strong>错误时间:</strong> <span id="error-time"></span></p>
            <p><strong>建议:</strong> 请稍后再试，或联系技术支持</p>
        </div>
    </div>
    
    <script>
        // 显示错误时间
        document.getElementById('error-time').textContent = new Date().toLocaleString('zh-CN');
        
        // 自动刷新倒计时（可选）
        let countdown = 30;
        const countdownElement = document.createElement('p');
        countdownElement.style.cssText = 'margin-top: 1rem; opacity: 0.7; font-size: 0.9rem;';
        countdownElement.innerHTML = `${countdown} 秒后自动刷新页面`;
        document.querySelector('.status-info').appendChild(countdownElement);
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.innerHTML = `${countdown} 秒后自动刷新页面`;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.reload();
            }
        }, 1000);
        
        // 点击任意按钮停止倒计时
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', () => {
                clearInterval(timer);
                countdownElement.style.display = 'none';
            });
        });
    </script>
</body>
</html>
