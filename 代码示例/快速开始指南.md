# 支付系统快速开始指南

## 🚀 快速部署

### 1. 环境要求

- **Java**: JDK 17+
- **Maven**: 3.6+
- **IDE**: IntelliJ IDEA 或 Eclipse
- **支付网关**: UniPay支付网关服务

### 2. 项目结构

```
game-server/
├── src/main/java/com/game/
│   ├── controller/          # 控制器层
│   │   ├── RechargeController.java
│   │   └── PaymentCallbackController.java
│   ├── service/            # 服务层
│   │   ├── RechargeService.java
│   │   ├── PaymentService.java
│   │   └── GameUserService.java
│   ├── entity/             # 实体类
│   │   ├── GameUser.java
│   │   └── RechargeOrder.java
│   ├── repository/         # 数据访问层
│   │   ├── GameUserRepository.java
│   │   └── RechargeOrderRepository.java
│   ├── config/             # 配置类
│   │   ├── PaymentConfig.java
│   │   └── GameConfig.java
│   └── util/               # 工具类
│       └── PaymentUtil.java
├── src/main/resources/
│   ├── application.yml     # 应用配置
│   └── templates/          # 前端模板
│       └── recharge.html
└── pom.xml                 # Maven配置
```

### 3. 一键启动步骤

#### 步骤1: 克隆或创建项目
```bash
# 创建项目目录
mkdir game-server
cd game-server

# 初始化Maven项目
mvn archetype:generate -DgroupId=com.game -DartifactId=game-server -DarchetypeArtifactId=maven-archetype-quickstart -DinteractiveMode=false
```

#### 步骤2: 配置pom.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.game</groupId>
    <artifactId>game-server</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.7</version>
        <relativePath/>
    </parent>
    
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    
    <dependencies>
        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- Spring Boot JPA -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        
        <!-- Thymeleaf模板引擎 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        
        <!-- H2数据库 -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>
        
        <!-- FastJSON -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        
        <!-- Apache HttpClient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        
        <!-- Apache Commons Codec -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

#### 步骤3: 配置application.yml
```yaml
server:
  port: 8088

spring:
  application:
    name: game-server
  
  # H2内存数据库配置
  datasource:
    url: jdbc:h2:mem:gamedb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  # H2控制台
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Thymeleaf配置
  thymeleaf:
    cache: false

# 支付网关配置（请替换为实际配置）
payment:
  gateway-url: http://47.110.87.166:9216
  mch-no: M1757512463
  app-id: 68c1830fd138c62623a963c0
  app-secret: 1234567890abcdef1234567890abcdef
  notify-url: http://47.110.87.166:8088/api/payment/notify
  return-url: http://47.110.87.166:8088/recharge/success

# 游戏配置
game:
  coin-rate: 100
  min-recharge-amount: 1.00
  max-recharge-amount: 10000.00
  initial-coin-balance: 1000

# 日志配置
logging:
  level:
    com.game: DEBUG
```

#### 步骤4: 复制核心代码文件
将以下代码文件复制到对应目录：

1. **代码示例/1-支付请求实现.java** → `src/main/java/com/game/controller/RechargeController.java`
2. **代码示例/2-订单创建实现.java** → `src/main/java/com/game/service/RechargeService.java` 和 `PaymentService.java`
3. **代码示例/3-支付回调处理.java** → `src/main/java/com/game/controller/PaymentCallbackController.java`
4. **代码示例/4-签名验证工具类.java** → `src/main/java/com/game/util/PaymentUtil.java`
5. **代码示例/5-配置文件和实体类.java** → 对应的配置类和实体类

#### 步骤5: 启动应用
```bash
# 编译并启动
mvn spring-boot:run

# 或者打包后启动
mvn clean package
java -jar target/game-server-1.0.0.jar
```

### 4. 测试流程

#### 4.1 访问充值页面
打开浏览器访问：`http://localhost:8088/recharge`

#### 4.2 创建充值订单
1. 选择充值金额（如：¥5 = 500游戏币）
2. 点击"生成支付宝二维码"按钮
3. 系统会显示支付二维码

#### 4.3 模拟支付回调
```bash
# 使用curl模拟支付成功回调
curl -X POST http://localhost:8088/api/payment/test-notify \
  -d "payOrderId=P1966019268248408065&state=2"
```

#### 4.4 查看结果
- 刷新充值页面，查看用户游戏币余额是否增加
- 查看充值记录状态是否变为"支付成功"

### 5. 数据库管理

#### 访问H2控制台
- URL: `http://localhost:8088/h2-console`
- JDBC URL: `jdbc:h2:mem:gamedb`
- 用户名: `sa`
- 密码: （空）

#### 查看数据表
```sql
-- 查看用户表
SELECT * FROM game_user;

-- 查看充值订单表
SELECT * FROM recharge_order;

-- 查看用户充值统计
SELECT username, coin_balance, total_recharge 
FROM game_user 
WHERE total_recharge > 0;
```

## 🔧 配置说明

### 支付网关配置
```yaml
payment:
  gateway-url: http://your-gateway-host:port    # 支付网关地址
  mch-no: YOUR_MERCHANT_NO                      # 商户号
  app-id: YOUR_APP_ID                           # 应用ID
  app-secret: YOUR_APP_SECRET                   # 应用密钥
  notify-url: http://your-domain/api/payment/notify  # 回调地址
  return-url: http://your-domain/recharge/success    # 成功跳转地址
```

### 游戏配置
```yaml
game:
  coin-rate: 100                    # 1元 = 100游戏币
  min-recharge-amount: 1.00         # 最小充值金额
  max-recharge-amount: 10000.00     # 最大充值金额
  initial-coin-balance: 1000        # 新用户初始游戏币
```

## 🚨 注意事项

### 1. 安全配置
- **生产环境必须使用HTTPS**
- **app-secret必须保密，不能暴露在前端**
- **回调地址必须是公网可访问的地址**

### 2. 数据库配置
```yaml
# 生产环境使用MySQL
spring:
  datasource:
    url: *************************************************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 3. 日志配置
```yaml
# 生产环境日志配置
logging:
  level:
    com.game: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
  file:
    name: logs/game-server.log
```

## 🔍 故障排查

### 常见问题

#### 1. 支付订单创建失败
- 检查支付网关地址是否正确
- 检查商户号和应用ID是否正确
- 检查网络连接是否正常

#### 2. 签名验证失败
- 检查app-secret是否正确
- 检查签名算法实现是否正确
- 查看日志中的签名原串和计算结果

#### 3. 回调处理失败
- 检查回调地址是否可访问
- 检查回调数据格式是否正确
- 查看异常日志定位具体问题

#### 4. 二维码不显示
- 检查前端JavaScript是否正确执行
- 检查支付网关返回的数据格式
- 查看浏览器控制台错误信息

### 调试技巧

#### 1. 启用详细日志
```yaml
logging:
  level:
    com.game: DEBUG
    org.springframework.web: DEBUG
```

#### 2. 查看HTTP请求
```java
// 在PaymentService中添加请求日志
log.info("支付请求URL: {}", requestUrl);
log.info("支付请求参数: {}", JSON.toJSONString(params));
log.info("支付响应结果: {}", response);
```

#### 3. 测试签名算法
```java
// 使用PaymentUtil.main()方法测试签名
public static void main(String[] args) {
    // 测试代码在PaymentUtil类中
}
```

## 📚 扩展功能

### 1. 支付方式扩展
- 微信支付
- 银联支付
- 数字货币支付

### 2. 业务功能扩展
- 充值优惠活动
- VIP等级系统
- 消费记录统计

### 3. 系统集成
- 用户认证系统
- 订单管理系统
- 财务对账系统

## 📞 技术支持

如果在使用过程中遇到问题，可以：

1. 查看详细的实现教程：`支付流程实现教程.md`
2. 参考代码示例：`代码示例/` 目录下的文件
3. 检查配置文件是否正确
4. 查看应用日志定位问题

---

**祝您使用愉快！** 🎉