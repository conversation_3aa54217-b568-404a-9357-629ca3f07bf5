# 支付流程实现教程

## 概述

本教程详细介绍了基于UniPay支付网关的完整支付流程实现，包括支付请求、订单创建、支付回调处理等核心功能。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   游戏客户端     │    │   游戏服务器     │    │   支付网关       │
│                │    │                │    │                │
│  - 充值界面     │◄──►│  - 订单管理     │◄──►│  - 支付处理     │
│  - 二维码显示   │    │  - 回调处理     │    │  - 第三方对接   │
│  - 状态查询     │    │  - 余额管理     │    │  - 签名验证     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 1. 支付请求实现

### 1.1 前端支付请求

**文件位置**: `game-server/src/main/resources/templates/recharge.html`

```javascript
function createRecharge() {
    const amount = document.getElementById('customAmount').value;
    if (!amount || amount <= 0) {
        alert('请输入有效的充值金额');
        return;
    }

    fetch('/recharge/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `username=${username}&amount=${amount}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const order = data.data;
            if (order.qrCodeUrl) {
                showQrCode(order.qrCodeUrl, order.gameOrderNo);
            } else {
                alert('充值订单创建成功！订单号: ' + order.gameOrderNo);
                location.reload();
            }
        } else {
            alert('创建订单失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('网络错误，请重试');
    });
}
```

### 1.2 后端控制器实现

**文件位置**: `game-server/src/main/java/com/game/controller/RechargeController.java`

```java
@PostMapping("/create")
@ResponseBody
public Map<String, Object> createRecharge(@RequestParam String username, 
                                        @RequestParam BigDecimal amount) {
    Map<String, Object> result = new HashMap<>();
    try {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            result.put("success", false);
            result.put("message", "充值金额必须大于0");
            return result;
        }

        JSONObject orderData = rechargeService.createRechargeOrder(username, amount);
        
        result.put("success", true);
        result.put("message", "充值订单创建成功");
        result.put("data", orderData);
        
    } catch (Exception e) {
        log.error("创建充值订单失败", e);
        result.put("success", false);
        result.put("message", "创建充值订单失败: " + e.getMessage());
    }
    return result;
}
```

## 2. 支付订单创建实现

### 2.1 服务层实现

**文件位置**: `game-server/src/main/java/com/game/service/RechargeService.java`

```java
@Transactional
public JSONObject createRechargeOrder(String username, BigDecimal amount) {
    // 获取或创建用户
    GameUser user = gameUserService.getOrCreateUser(username);

    // 计算游戏币数量
    BigDecimal coinAmount = amount.multiply(new BigDecimal(gameConfig.getCoinRate()));

    // 创建充值订单
    RechargeOrder order = new RechargeOrder();
    order.setGameOrderNo(PaymentUtil.generateOrderNo());
    order.setUserId(user.getId());
    order.setUsername(username);
    order.setAmount(amount);
    order.setCoinAmount(coinAmount);
    order.setStatus(RechargeOrder.STATUS_PENDING);
    order.setPayWay("ALI_QR");

    // 保存订单
    order = rechargeOrderRepository.save(order);

    // 调用支付网关创建支付订单
    try {
        String subject = "游戏充值 - " + coinAmount + "游戏币";
        String body = "用户 " + username + " 充值 " + amount + "元，获得 " + coinAmount + " 游戏币";
        
        JSONObject payResult = paymentService.createPayOrder(order.getGameOrderNo(), amount, subject, body);
        
        if (payResult.getInteger("code") == 0) {
            JSONObject data = payResult.getJSONObject("data");
            order.setPayOrderId(data.getString("payOrderId"));
            order = rechargeOrderRepository.save(order);
            log.info("充值订单创建成功: {}", order.getGameOrderNo());
            
            // 构造返回结果，包含订单信息和二维码URL
            JSONObject result = new JSONObject();
            result.put("id", order.getId());
            result.put("gameOrderNo", order.getGameOrderNo());
            result.put("payOrderId", order.getPayOrderId());
            result.put("amount", order.getAmount());
            result.put("coinAmount", order.getCoinAmount());
            result.put("status", order.getStatus());
            result.put("qrCodeUrl", data.getString("payData")); // 二维码URL
            result.put("createTime", order.getCreateTime());
            
            return result;
        } else {
            throw new RuntimeException("支付订单创建失败: " + payResult.getString("msg"));
        }
    } catch (Exception e) {
        log.error("创建支付订单失败", e);
        throw new RuntimeException("创建支付订单失败: " + e.getMessage());
    }
}
```

### 2.2 支付网关接口调用

**文件位置**: `game-server/src/main/java/com/game/service/PaymentService.java`

```java
public JSONObject createPayOrder(String mchOrderNo, BigDecimal amount, String subject, String body) {
    try {
        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("mchNo", paymentConfig.getMchNo());
        params.put("appId", paymentConfig.getAppId());
        params.put("mchOrderNo", mchOrderNo);
        params.put("wayCode", "ALI_QR");
        params.put("amount", amount.multiply(new BigDecimal(100)).longValue()); // 转换为分
        params.put("currency", "cny");
        params.put("clientIp", "127.0.0.1");
        params.put("subject", subject);
        params.put("body", body);
        params.put("notifyUrl", paymentConfig.getNotifyUrl());
        params.put("returnUrl", paymentConfig.getReturnUrl());
        params.put("reqTime", System.currentTimeMillis());
        params.put("version", "1.0");
        params.put("signType", "MD5");
        params.put("channelExtra", "{}");
        params.put("extParam", "{}");

        // 生成签名
        String sign = PaymentUtil.generateSign(params, paymentConfig.getAppSecret());
        params.put("sign", sign);

        log.info("支付订单创建请求: {}", JSON.toJSONString(params));

        // 发送HTTP请求
        String response = sendHttpRequest(paymentConfig.getGatewayUrl() + "/api/pay/unifiedOrder", params);
        
        log.info("支付订单创建响应: {}", response);
        
        return JSON.parseObject(response);
        
    } catch (Exception e) {
        log.error("创建支付订单异常", e);
        throw new RuntimeException("创建支付订单失败: " + e.getMessage());
    }
}
```

## 3. 支付成功回调处理

### 3.1 回调接口实现

**文件位置**: `game-server/src/main/java/com/game/controller/PaymentCallbackController.java`

```java
@PostMapping("/notify")
public String paymentNotify(@RequestBody String requestBody) {
    log.info("收到支付回调: {}", requestBody);
    
    try {
        // 解析URL编码的表单数据
        Map<String, Object> params = parseFormData(requestBody);
        
        // 验证签名
        String sign = (String) params.get("sign");
        if (sign == null) {
            log.error("支付回调缺少签名");
            return "fail";
        }
        
        // 创建验证签名用的参数Map（不包含sign）
        Map<String, Object> signParams = new HashMap<>(params);
        signParams.remove("sign");
        
        boolean signValid = PaymentUtil.verifySign(signParams, paymentConfig.getAppSecret(), sign);
        if (!signValid) {
            log.error("支付回调签名验证失败");
            return "fail";
        }

        // 获取订单信息
        String payOrderId = (String) params.get("payOrderId");
        if (payOrderId == null) {
            log.error("支付回调缺少payOrderId");
            return "fail";
        }

        // 将参数转换为JSON字符串传递给服务层
        JSONObject callbackJson = new JSONObject(params);
        boolean success = rechargeService.handlePaymentCallback(payOrderId, callbackJson.toJSONString());
        
        if (success) {
            log.info("支付回调处理成功: {}", payOrderId);
            return "success";
        } else {
            log.error("支付回调处理失败: {}", payOrderId);
            return "fail";
        }
        
    } catch (Exception e) {
        log.error("处理支付回调异常", e);
        return "fail";
    }
}

/**
 * 解析URL编码的表单数据
 */
private Map<String, Object> parseFormData(String formData) {
    Map<String, Object> params = new HashMap<>();
    if (formData == null || formData.trim().isEmpty()) {
        return params;
    }
    
    try {
        String[] pairs = formData.split("&");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                String key = java.net.URLDecoder.decode(keyValue[0], "UTF-8");
                String value = java.net.URLDecoder.decode(keyValue[1], "UTF-8");
                params.put(key, value);
            }
        }
    } catch (Exception e) {
        log.error("解析表单数据失败", e);
    }
    
    return params;
}
```

### 3.2 回调业务处理

**文件位置**: `game-server/src/main/java/com/game/service/RechargeService.java`

```java
@Transactional
public boolean handlePaymentCallback(String payOrderId, String callbackData) {
    try {
        log.info("处理支付回调: payOrderId={}, data={}", payOrderId, callbackData);

        // 查找充值订单
        RechargeOrder order = rechargeOrderRepository.findByPayOrderId(payOrderId).orElse(null);
        if (order == null) {
            log.warn("未找到充值订单: {}", payOrderId);
            return false;
        }

        // 检查订单状态
        if (order.getStatus() != RechargeOrder.STATUS_PENDING) {
            log.warn("订单状态异常: {}, status={}", payOrderId, order.getStatus());
            return true; // 已处理过，返回成功
        }

        // 解析回调数据
        JSONObject callbackJson = JSON.parseObject(callbackData);
        Integer state = callbackJson.getInteger("state");

        if (state != null && state == 2) { // 支付成功
            // 更新订单状态
            order.setStatus(RechargeOrder.STATUS_SUCCESS);
            order.setPayTime(LocalDateTime.now());
            order.setCallbackData(callbackData);
            rechargeOrderRepository.save(order);

            // 增加用户游戏币
            boolean success = gameUserService.addCoinBalance(order.getUserId(), order.getCoinAmount(), order.getAmount());
            if (success) {
                log.info("充值成功: 用户={}, 订单={}, 金额={}, 游戏币={}", 
                        order.getUsername(), order.getGameOrderNo(), order.getAmount(), order.getCoinAmount());
                return true;
            } else {
                log.error("增加游戏币失败: {}", order.getGameOrderNo());
                return false;
            }
        } else {
            // 支付失败
            order.setStatus(RechargeOrder.STATUS_FAILED);
            order.setCallbackData(callbackData);
            order.setRemark("支付失败，状态码: " + state);
            rechargeOrderRepository.save(order);
            log.info("支付失败: {}", order.getGameOrderNo());
            return true;
        }

    } catch (Exception e) {
        log.error("处理支付回调失败", e);
        return false;
    }
}
```

## 4. 签名验证工具类

### 4.1 签名生成和验证

**文件位置**: `game-server/src/main/java/com/game/util/PaymentUtil.java`

```java
public class PaymentUtil {
    
    /**
     * 生成签名
     */
    public static String generateSign(Map<String, Object> params, String secret) {
        // 过滤空值参数
        Map<String, Object> filteredParams = params.entrySet().stream()
                .filter(entry -> entry.getValue() != null && !entry.getValue().toString().isEmpty())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 按key排序
        String sortedParams = filteredParams.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));

        // 拼接密钥
        String signStr = sortedParams + "&key=" + secret;
        
        // MD5加密并转大写
        return DigestUtils.md5Hex(signStr).toUpperCase();
    }

    /**
     * 验证签名
     */
    public static boolean verifySign(Map<String, Object> params, String secret, String sign) {
        String generatedSign = generateSign(params, secret);
        return generatedSign.equals(sign);
    }

    /**
     * 生成订单号
     */
    public static String generateOrderNo() {
        return "G" + System.currentTimeMillis() + String.format("%04d", new Random().nextInt(10000));
    }
}
```

## 5. 配置文件

### 5.1 支付配置

**文件位置**: `game-server/src/main/java/com/game/config/PaymentConfig.java`

```java
@Configuration
@ConfigurationProperties(prefix = "payment")
@Data
public class PaymentConfig {
    private String gatewayUrl;
    private String mchNo;
    private String appId;
    private String appSecret;
    private String notifyUrl;
    private String returnUrl;
}
```

### 5.2 应用配置

**文件位置**: `game-server/src/main/resources/application.yml`

```yaml
server:
  port: 8088

payment:
  gateway-url: http://*************:9216
  mch-no: M1757512463
  app-id: 68c1830fd138c62623a963c0
  app-secret: 1234567890abcdef1234567890abcdef
  notify-url: http://*************:8088/api/payment/notify
  return-url: http://*************:8088/recharge/success

game:
  coin-rate: 100  # 1元 = 100游戏币
```

## 6. 数据库实体

### 6.1 充值订单实体

**文件位置**: `game-server/src/main/java/com/game/entity/RechargeOrder.java`

```java
@Data
@Entity
@Table(name = "recharge_order")
public class RechargeOrder {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    private String gameOrderNo;        // 游戏订单号

    private String payOrderId;         // 支付网关订单号
    private Long userId;               // 用户ID
    private String username;           // 用户名

    @Column(precision = 10, scale = 2)
    private BigDecimal amount;         // 充值金额（元）

    @Column(precision = 15, scale = 2)
    private BigDecimal coinAmount;     // 获得游戏币数量

    private Integer status = 0;        // 订单状态：0-待支付 1-支付成功 2-支付失败 3-已取消
    private String payWay;             // 支付方式
    private LocalDateTime payTime;     // 支付时间

    @Column(columnDefinition = "TEXT")
    private String callbackData;       // 支付网关回调数据

    private String remark;             // 备注

    @CreationTimestamp
    private LocalDateTime createTime;  // 创建时间

    @UpdateTimestamp
    private LocalDateTime updateTime;  // 更新时间

    // 订单状态常量
    public static final int STATUS_PENDING = 0;   // 待支付
    public static final int STATUS_SUCCESS = 1;   // 支付成功
    public static final int STATUS_FAILED = 2;    // 支付失败
    public static final int STATUS_CANCELLED = 3; // 已取消
}
```

### 6.2 用户实体

**文件位置**: `game-server/src/main/java/com/game/entity/GameUser.java`

```java
@Data
@Entity
@Table(name = "game_user")
public class GameUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    private String username;           // 用户名

    private String nickname;           // 昵称

    @Column(precision = 15, scale = 2)
    private BigDecimal coinBalance = BigDecimal.ZERO;    // 游戏币余额

    @Column(precision = 15, scale = 2)
    private BigDecimal totalRecharge = BigDecimal.ZERO;  // 累计充值金额

    private Integer status = 1;        // 用户状态：1-正常 0-禁用

    @CreationTimestamp
    private LocalDateTime createTime;  // 创建时间

    @UpdateTimestamp
    private LocalDateTime updateTime;  // 更新时间
}
```

## 7. 前端二维码显示

### 7.1 二维码显示函数

```javascript
function showQrCode(qrCodeUrl, orderNo) {
    const qrCodeContainer = document.getElementById('qrCodeContainer');
    qrCodeContainer.innerHTML = `
        <div class="mb-3">
            <h5>订单号: ${orderNo}</h5>
        </div>
        <div class="mb-3">
            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCodeUrl)}" 
                 alt="支付二维码" class="img-fluid" style="max-width: 200px;">
        </div>
        <div class="mb-2">
            <small class="text-muted">二维码内容: ${qrCodeUrl}</small>
        </div>
    `;
    document.getElementById('qrCodeCard').style.display = 'block';
    
    // 滚动到二维码区域
    document.getElementById('qrCodeCard').scrollIntoView({ behavior: 'smooth' });
}
```

## 8. 测试流程

### 8.1 完整测试步骤

1. **启动服务**
   ```bash
   cd game-server
   mvn spring-boot:run
   ```

2. **访问充值页面**
   ```
   http://*************:8088/recharge
   ```

3. **创建充值订单**
   - 选择充值金额（如¥20 = 2000游戏币）
   - 点击"生成支付宝二维码"按钮

4. **支付测试**
   - 使用支付宝扫描生成的二维码
   - 完成支付流程

5. **验证结果**
   - 检查用户游戏币余额是否增加
   - 查看充值记录状态是否更新为"已完成"

### 8.2 日志监控

关键日志输出：
```
支付订单创建请求: {...}
支付订单创建响应: {...}
充值订单创建成功: G17575708935570653
收到支付回调: ifCode=alipay&amount=2000&...
充值成功: 用户=testuser, 订单=G17575708935570653, 金额=20, 游戏币=2000
```

## 9. 常见问题和解决方案

### 9.1 签名验证失败
- 检查参数排序是否正确
- 确认密钥配置是否正确
- 验证参数值是否包含特殊字符

### 9.2 回调数据解析失败
- 确认数据格式为URL编码格式
- 检查字符编码是否为UTF-8
- 验证参数解析逻辑

### 9.3 订单状态异常
- 检查数据库事务配置
- 确认订单状态更新逻辑
- 验证并发处理机制

## 10. 安全注意事项

1. **签名验证**：所有回调必须验证签名
2. **幂等性**：支付回调处理必须支持重复调用
3. **状态检查**：避免重复处理已完成的订单
4. **异常处理**：完善的异常处理和日志记录
5. **数据校验**：严格校验所有输入参数

## 总结

本教程提供了完整的支付流程实现方案，包括：
- 前端支付请求和二维码显示
- 后端订单创建和管理
- 支付网关接口调用
- 支付回调处理和验证
- 用户余额管理
- 完整的测试流程

通过本教程，可以快速搭建一个安全、可靠的支付系统。