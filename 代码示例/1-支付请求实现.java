/**
 * 支付请求实现示例
 * 文件位置: game-server/src/main/java/com/game/controller/RechargeController.java
 */

@Slf4j
@Controller
@RequestMapping("/recharge")
public class RechargeController {

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private GameUserService gameUserService;

    /**
     * 创建充值订单 - 核心支付请求方法
     * 
     * @param username 用户名
     * @param amount 充值金额
     * @return 包含订单信息和二维码URL的响应
     */
    @PostMapping("/create")
    @ResponseBody
    public Map<String, Object> createRecharge(@RequestParam String username, 
                                            @RequestParam BigDecimal amount) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 参数验证
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                result.put("success", false);
                result.put("message", "充值金额必须大于0");
                return result;
            }

            if (amount.compareTo(new BigDecimal("10000")) > 0) {
                result.put("success", false);
                result.put("message", "单次充值金额不能超过10000元");
                return result;
            }

            // 2. 调用服务层创建订单
            JSONObject orderData = rechargeService.createRechargeOrder(username, amount);
            
            // 3. 返回成功响应
            result.put("success", true);
            result.put("message", "充值订单创建成功");
            result.put("data", orderData);
            
            log.info("用户 {} 创建充值订单成功，金额: {}", username, amount);
            
        } catch (IllegalArgumentException e) {
            // 业务异常
            log.warn("创建充值订单参数错误: {}", e.getMessage());
            result.put("success", false);
            result.put("message", e.getMessage());
        } catch (Exception e) {
            // 系统异常
            log.error("创建充值订单失败", e);
            result.put("success", false);
            result.put("message", "系统繁忙，请稍后重试");
        }
        
        return result;
    }

    /**
     * 查询订单状态
     * 
     * @param gameOrderNo 游戏订单号
     * @return 订单状态信息
     */
    @GetMapping("/query/{gameOrderNo}")
    @ResponseBody
    public Map<String, Object> queryOrder(@PathVariable String gameOrderNo) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            RechargeOrder order = rechargeService.getOrderByGameOrderNo(gameOrderNo);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            // 查询支付网关订单状态
            JSONObject payStatus = rechargeService.queryPayOrderStatus(gameOrderNo);
            
            result.put("success", true);
            result.put("order", order);
            result.put("payStatus", payStatus);
            
        } catch (Exception e) {
            log.error("查询订单失败", e);
            result.put("success", false);
            result.put("message", "查询订单失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 充值页面
     */
    @GetMapping("")
    public String rechargePage(@RequestParam(defaultValue = "testuser") String username, Model model) {
        GameUser user = gameUserService.getOrCreateUser(username);
        List<RechargeOrder> orders = rechargeService.getOrdersByUsername(username);
        
        model.addAttribute("user", user);
        model.addAttribute("orders", orders);
        return "recharge";
    }
}