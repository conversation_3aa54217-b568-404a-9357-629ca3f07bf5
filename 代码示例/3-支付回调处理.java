/**
 * 支付回调处理实现示例
 * 文件位置: game-server/src/main/java/com/game/controller/PaymentCallbackController.java
 */

@Slf4j
@RestController
@RequestMapping("/api/payment")
public class PaymentCallbackController {

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private PaymentConfig paymentConfig;

    /**
     * 支付回调接口 - 核心回调处理方法
     * 
     * @param requestBody 支付网关回调的原始数据
     * @return success/fail 告知支付网关处理结果
     */
    @PostMapping("/notify")
    public String paymentNotify(@RequestBody String requestBody) {
        log.info("收到支付回调: {}", requestBody);
        
        try {
            // 1. 解析URL编码的表单数据
            Map<String, Object> params = parseFormData(requestBody);
            log.info("解析回调参数: {}", params);
            
            // 2. 验证必要参数
            String sign = (String) params.get("sign");
            String payOrderId = (String) params.get("payOrderId");
            String mchOrderNo = (String) params.get("mchOrderNo");
            Integer state = Integer.valueOf(params.get("state").toString());
            
            if (sign == null || payOrderId == null || mchOrderNo == null) {
                log.error("支付回调缺少必要参数: sign={}, payOrderId={}, mchOrderNo={}", 
                         sign, payOrderId, mchOrderNo);
                return "fail";
            }
            
            // 3. 验证签名
            Map<String, Object> signParams = new HashMap<>(params);
            signParams.remove("sign"); // 签名验证时需要移除sign参数
            
            boolean signValid = PaymentUtil.verifySign(signParams, paymentConfig.getAppSecret(), sign);
            if (!signValid) {
                log.error("支付回调签名验证失败: 计算签名与回调签名不匹配");
                return "fail";
            }
            
            log.info("支付回调签名验证成功");

            // 4. 验证商户号和应用ID
            String mchNo = (String) params.get("mchNo");
            String appId = (String) params.get("appId");
            
            if (!paymentConfig.getMchNo().equals(mchNo) || !paymentConfig.getAppId().equals(appId)) {
                log.error("支付回调商户信息不匹配: mchNo={}, appId={}", mchNo, appId);
                return "fail";
            }

            // 5. 处理支付回调业务逻辑
            JSONObject callbackJson = new JSONObject(params);
            boolean success = rechargeService.handlePaymentCallback(payOrderId, callbackJson.toJSONString());
            
            if (success) {
                log.info("支付回调处理成功: payOrderId={}, mchOrderNo={}, state={}", 
                        payOrderId, mchOrderNo, state);
                return "success";
            } else {
                log.error("支付回调业务处理失败: payOrderId={}", payOrderId);
                return "fail";
            }
            
        } catch (NumberFormatException e) {
            log.error("支付回调参数格式错误", e);
            return "fail";
        } catch (Exception e) {
            log.error("处理支付回调异常", e);
            return "fail";
        }
    }

    /**
     * 解析URL编码的表单数据
     * 
     * @param formData URL编码的表单数据字符串
     * @return 解析后的参数Map
     */
    private Map<String, Object> parseFormData(String formData) {
        Map<String, Object> params = new HashMap<>();
        
        if (formData == null || formData.trim().isEmpty()) {
            return params;
        }
        
        try {
            // 按&分割参数对
            String[] pairs = formData.split("&");
            
            for (String pair : pairs) {
                // 按=分割键值对
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    String key = URLDecoder.decode(keyValue[0], "UTF-8");
                    String value = URLDecoder.decode(keyValue[1], "UTF-8");
                    params.put(key, value);
                }
            }
            
        } catch (UnsupportedEncodingException e) {
            log.error("解析表单数据编码异常", e);
        } catch (Exception e) {
            log.error("解析表单数据失败", e);
        }
        
        return params;
    }

    /**
     * 测试回调接口 - 用于开发测试
     */
    @PostMapping("/test-notify")
    public String testNotify(@RequestParam String payOrderId, 
                           @RequestParam(defaultValue = "2") Integer state) {
        log.info("测试支付回调: payOrderId={}, state={}", payOrderId, state);
        
        try {
            // 构造测试回调数据
            JSONObject testData = new JSONObject();
            testData.put("payOrderId", payOrderId);
            testData.put("mchOrderNo", "test_order_" + System.currentTimeMillis());
            testData.put("state", state);
            testData.put("amount", 100L);
            testData.put("mchNo", paymentConfig.getMchNo());
            testData.put("appId", paymentConfig.getAppId());
            testData.put("reqTime", System.currentTimeMillis());
            testData.put("wayCode", "ALI_QR");
            testData.put("ifCode", "alipay");
            
            // 生成签名
            Map<String, Object> params = new HashMap<>();
            for (String key : testData.keySet()) {
                params.put(key, testData.get(key));
            }
            String sign = PaymentUtil.generateSign(params, paymentConfig.getAppSecret());
            testData.put("sign", sign);
            
            // 处理回调
            boolean success = rechargeService.handlePaymentCallback(payOrderId, testData.toJSONString());
            
            return success ? "success" : "fail";
            
        } catch (Exception e) {
            log.error("测试支付回调异常", e);
            return "fail";
        }
    }
}

/**
 * 支付回调业务处理实现
 * 文件位置: game-server/src/main/java/com/game/service/RechargeService.java
 */

@Slf4j
@Service
public class RechargeService {

    @Autowired
    private RechargeOrderRepository rechargeOrderRepository;

    @Autowired
    private GameUserService gameUserService;

    /**
     * 处理支付回调 - 核心业务处理方法
     * 
     * @param payOrderId 支付网关订单号
     * @param callbackData 回调数据JSON字符串
     * @return 处理结果
     */
    @Transactional
    public boolean handlePaymentCallback(String payOrderId, String callbackData) {
        try {
            log.info("开始处理支付回调: payOrderId={}", payOrderId);

            // 1. 查找充值订单
            Optional<RechargeOrder> orderOpt = rechargeOrderRepository.findByPayOrderId(payOrderId);
            if (!orderOpt.isPresent()) {
                log.warn("未找到对应的充值订单: payOrderId={}", payOrderId);
                return false;
            }

            RechargeOrder order = orderOpt.get();
            log.info("找到充值订单: gameOrderNo={}, username={}, amount={}, status={}", 
                    order.getGameOrderNo(), order.getUsername(), order.getAmount(), order.getStatus());

            // 2. 检查订单状态，避免重复处理
            if (order.getStatus() != RechargeOrder.STATUS_PENDING) {
                log.warn("订单状态异常，可能已处理: payOrderId={}, currentStatus={}", 
                        payOrderId, order.getStatus());
                
                // 如果订单已经是成功状态，返回true（幂等性处理）
                return order.getStatus() == RechargeOrder.STATUS_SUCCESS;
            }

            // 3. 解析回调数据
            JSONObject callbackJson = JSON.parseObject(callbackData);
            Integer state = callbackJson.getInteger("state");
            Long amount = callbackJson.getLong("amount");
            String channelOrderNo = callbackJson.getString("channelOrderNo");
            Long successTime = callbackJson.getLong("successTime");

            log.info("回调数据解析: state={}, amount={}, channelOrderNo={}, successTime={}", 
                    state, amount, channelOrderNo, successTime);

            // 4. 验证金额是否匹配
            if (amount != null) {
                BigDecimal callbackAmount = new BigDecimal(amount).divide(new BigDecimal(100)); // 分转元
                if (callbackAmount.compareTo(order.getAmount()) != 0) {
                    log.error("回调金额与订单金额不匹配: 订单金额={}, 回调金额={}", 
                             order.getAmount(), callbackAmount);
                    return false;
                }
            }

            // 5. 根据支付状态处理订单
            if (state != null && state == 2) {
                // 支付成功
                return handlePaymentSuccess(order, callbackData, successTime);
            } else {
                // 支付失败或其他状态
                return handlePaymentFailure(order, callbackData, state);
            }

        } catch (Exception e) {
            log.error("处理支付回调异常: payOrderId={}", payOrderId, e);
            return false;
        }
    }

    /**
     * 处理支付成功
     */
    private boolean handlePaymentSuccess(RechargeOrder order, String callbackData, Long successTime) {
        try {
            log.info("处理支付成功: 订单号={}, 用户={}, 金额={}, 游戏币={}", 
                    order.getGameOrderNo(), order.getUsername(), order.getAmount(), order.getCoinAmount());

            // 1. 更新订单状态
            order.setStatus(RechargeOrder.STATUS_SUCCESS);
            order.setCallbackData(callbackData);
            
            if (successTime != null) {
                order.setPayTime(LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(successTime), ZoneId.systemDefault()));
            } else {
                order.setPayTime(LocalDateTime.now());
            }
            
            order.setRemark("支付成功");
            order = rechargeOrderRepository.save(order);

            // 2. 增加用户游戏币余额
            boolean balanceUpdated = gameUserService.addCoinBalance(
                order.getUserId(), 
                order.getCoinAmount(), 
                order.getAmount()
            );

            if (balanceUpdated) {
                log.info("充值成功完成: 用户={}, 订单={}, 充值金额={}, 获得游戏币={}", 
                        order.getUsername(), order.getGameOrderNo(), 
                        order.getAmount(), order.getCoinAmount());

                // 3. 发送充值成功通知（可选）
                sendRechargeSuccessNotification(order);
                
                return true;
            } else {
                log.error("增加用户游戏币失败: 订单={}", order.getGameOrderNo());
                
                // 回滚订单状态
                order.setStatus(RechargeOrder.STATUS_FAILED);
                order.setRemark("增加游戏币失败");
                rechargeOrderRepository.save(order);
                
                return false;
            }

        } catch (Exception e) {
            log.error("处理支付成功异常: 订单={}", order.getGameOrderNo(), e);
            return false;
        }
    }

    /**
     * 处理支付失败
     */
    private boolean handlePaymentFailure(RechargeOrder order, String callbackData, Integer state) {
        try {
            log.info("处理支付失败: 订单号={}, 状态码={}", order.getGameOrderNo(), state);

            // 更新订单状态为失败
            order.setStatus(RechargeOrder.STATUS_FAILED);
            order.setCallbackData(callbackData);
            order.setRemark("支付失败，状态码: " + state);
            rechargeOrderRepository.save(order);

            log.info("支付失败处理完成: 订单={}", order.getGameOrderNo());
            
            // 发送支付失败通知（可选）
            sendRechargeFailureNotification(order);
            
            return true; // 失败也返回true，表示回调处理完成

        } catch (Exception e) {
            log.error("处理支付失败异常: 订单={}", order.getGameOrderNo(), e);
            return false;
        }
    }

    /**
     * 发送充值成功通知
     */
    private void sendRechargeSuccessNotification(RechargeOrder order) {
        try {
            // 这里可以实现各种通知方式：
            // 1. 发送邮件通知
            // 2. 发送短信通知  
            // 3. 推送消息到客户端
            // 4. 记录操作日志
            
            log.info("发送充值成功通知: 用户={}, 订单={}", order.getUsername(), order.getGameOrderNo());
            
        } catch (Exception e) {
            log.error("发送充值成功通知失败", e);
        }
    }

    /**
     * 发送充值失败通知
     */
    private void sendRechargeFailureNotification(RechargeOrder order) {
        try {
            log.info("发送充值失败通知: 用户={}, 订单={}", order.getUsername(), order.getGameOrderNo());
            
        } catch (Exception e) {
            log.error("发送充值失败通知失败", e);
        }
    }
}