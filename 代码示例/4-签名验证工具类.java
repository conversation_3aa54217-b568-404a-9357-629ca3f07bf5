/**
 * 签名验证和工具类实现示例
 * 文件位置: game-server/src/main/java/com/game/util/PaymentUtil.java
 */

import org.apache.commons.codec.digest.DigestUtils;
import java.util.*;
import java.util.stream.Collectors;

public class PaymentUtil {
    
    /**
     * 生成MD5签名 - 核心签名生成方法
     * 
     * @param params 参数Map
     * @param secret 签名密钥
     * @return MD5签名字符串（大写）
     */
    public static String generateSign(Map<String, Object> params, String secret) {
        try {
            // 1. 过滤空值参数
            Map<String, Object> filteredParams = params.entrySet().stream()
                    .filter(entry -> entry.getValue() != null)
                    .filter(entry -> !entry.getValue().toString().trim().isEmpty())
                    .filter(entry -> !"sign".equals(entry.getKey())) // 排除sign字段
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            // 2. 按key进行ASCII码排序
            String sortedParams = filteredParams.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining("&"));

            // 3. 拼接签名密钥
            String signStr = sortedParams + "&key=" + secret;
            
            System.out.println("签名原串: " + signStr); // 调试用，生产环境需要移除
            
            // 4. MD5加密并转大写
            String sign = DigestUtils.md5Hex(signStr).toUpperCase();
            
            System.out.println("生成签名: " + sign); // 调试用，生产环境需要移除
            
            return sign;
            
        } catch (Exception e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * 验证签名 - 核心签名验证方法
     * 
     * @param params 参数Map（不包含sign字段）
     * @param secret 签名密钥
     * @param receivedSign 接收到的签名
     * @return 验证结果
     */
    public static boolean verifySign(Map<String, Object> params, String secret, String receivedSign) {
        try {
            if (receivedSign == null || receivedSign.trim().isEmpty()) {
                System.out.println("签名验证失败: 接收到的签名为空");
                return false;
            }
            
            // 生成本地签名
            String localSign = generateSign(params, secret);
            
            // 比较签名（忽略大小写）
            boolean isValid = localSign.equalsIgnoreCase(receivedSign);
            
            System.out.println("签名验证结果: " + isValid + 
                             ", 本地签名: " + localSign + 
                             ", 接收签名: " + receivedSign);
            
            return isValid;
            
        } catch (Exception e) {
            System.out.println("签名验证异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 生成订单号
     * 格式: G + 时间戳 + 4位随机数
     */
    public static String generateOrderNo() {
        long timestamp = System.currentTimeMillis();
        int random = new Random().nextInt(10000);
        return String.format("G%d%04d", timestamp, random);
    }

    /**
     * 生成支付订单号
     * 格式: P + 时间戳 + 4位随机数
     */
    public static String generatePayOrderNo() {
        long timestamp = System.currentTimeMillis();
        int random = new Random().nextInt(10000);
        return String.format("P%d%04d", timestamp, random);
    }

    /**
     * 参数Map转URL编码字符串
     */
    public static String mapToUrlEncoded(Map<String, Object> params) {
        try {
            return params.entrySet().stream()
                    .filter(entry -> entry.getValue() != null)
                    .map(entry -> {
                        try {
                            return URLEncoder.encode(entry.getKey(), "UTF-8") + "=" + 
                                   URLEncoder.encode(entry.getValue().toString(), "UTF-8");
                        } catch (UnsupportedEncodingException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .collect(Collectors.joining("&"));
        } catch (Exception e) {
            throw new RuntimeException("参数编码失败", e);
        }
    }

    /**
     * URL编码字符串转参数Map
     */
    public static Map<String, String> urlEncodedToMap(String urlEncoded) {
        Map<String, String> params = new HashMap<>();
        
        if (urlEncoded == null || urlEncoded.trim().isEmpty()) {
            return params;
        }
        
        try {
            String[] pairs = urlEncoded.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    String key = URLDecoder.decode(keyValue[0], "UTF-8");
                    String value = URLDecoder.decode(keyValue[1], "UTF-8");
                    params.put(key, value);
                }
            }
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("URL解码失败", e);
        }
        
        return params;
    }

    /**
     * 验证金额格式
     */
    public static boolean isValidAmount(BigDecimal amount) {
        if (amount == null) {
            return false;
        }
        
        // 金额必须大于0
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        // 金额不能超过100000元
        if (amount.compareTo(new BigDecimal("100000")) > 0) {
            return false;
        }
        
        // 金额精度不能超过2位小数
        if (amount.scale() > 2) {
            return false;
        }
        
        return true;
    }

    /**
     * 元转分
     */
    public static Long yuanToFen(BigDecimal yuan) {
        if (yuan == null) {
            return null;
        }
        return yuan.multiply(new BigDecimal(100)).longValue();
    }

    /**
     * 分转元
     */
    public static BigDecimal fenToYuan(Long fen) {
        if (fen == null) {
            return null;
        }
        return new BigDecimal(fen).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
    }

    /**
     * 生成随机字符串
     */
    public static String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return sb.toString();
    }

    /**
     * 验证订单号格式
     */
    public static boolean isValidOrderNo(String orderNo) {
        if (orderNo == null || orderNo.trim().isEmpty()) {
            return false;
        }
        
        // 订单号格式: G + 13位时间戳 + 4位随机数 = 18位
        if (orderNo.length() != 18) {
            return false;
        }
        
        // 必须以G开头
        if (!orderNo.startsWith("G")) {
            return false;
        }
        
        // 后17位必须是数字
        String numberPart = orderNo.substring(1);
        try {
            Long.parseLong(numberPart);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 格式化金额显示
     */
    public static String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0.00";
        }
        
        DecimalFormat df = new DecimalFormat("#,##0.00");
        return df.format(amount);
    }

    /**
     * 计算游戏币数量
     */
    public static BigDecimal calculateCoinAmount(BigDecimal amount, int coinRate) {
        if (amount == null || coinRate <= 0) {
            return BigDecimal.ZERO;
        }
        
        return amount.multiply(new BigDecimal(coinRate));
    }

    /**
     * 验证手机号格式
     */
    public static boolean isValidMobile(String mobile) {
        if (mobile == null || mobile.trim().isEmpty()) {
            return false;
        }
        
        String regex = "^1[3-9]\\d{9}$";
        return mobile.matches(regex);
    }

    /**
     * 验证邮箱格式
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        String regex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        return email.matches(regex);
    }

    /**
     * 生成支付链接
     */
    public static String generatePayUrl(String baseUrl, Map<String, Object> params, String secret) {
        // 生成签名
        String sign = generateSign(params, secret);
        params.put("sign", sign);
        
        // 构造URL
        String queryString = mapToUrlEncoded(params);
        return baseUrl + "?" + queryString;
    }

    /**
     * 日期格式化
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime.format(formatter);
    }

    /**
     * 解析日期时间
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return LocalDateTime.parse(dateTimeStr, formatter);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 签名验证示例测试
     */
    public static void main(String[] args) {
        // 测试签名生成和验证
        Map<String, Object> params = new HashMap<>();
        params.put("mchNo", "M1757512463");
        params.put("appId", "68c1830fd138c62623a963c0");
        params.put("mchOrderNo", "G17575708935570653");
        params.put("amount", 2000L);
        params.put("wayCode", "ALI_QR");
        params.put("currency", "cny");
        params.put("reqTime", 1757570893860L);
        
        String secret = "1234567890abcdef1234567890abcdef";
        
        // 生成签名
        String sign = generateSign(params, secret);
        System.out.println("生成的签名: " + sign);
        
        // 验证签名
        boolean isValid = verifySign(params, secret, sign);
        System.out.println("签名验证结果: " + isValid);
        
        // 测试订单号生成
        String orderNo = generateOrderNo();
        System.out.println("生成的订单号: " + orderNo);
        System.out.println("订单号格式验证: " + isValidOrderNo(orderNo));
    }
}