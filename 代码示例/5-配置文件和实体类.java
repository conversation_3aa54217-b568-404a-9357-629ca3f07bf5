/**
 * 配置文件和实体类实现示例
 */

// ==================== 支付配置类 ====================
/**
 * 支付网关配置
 * 文件位置: game-server/src/main/java/com/game/config/PaymentConfig.java
 */

@Configuration
@ConfigurationProperties(prefix = "payment")
@Data
public class PaymentConfig {
    
    /**
     * 支付网关地址
     */
    private String gatewayUrl;
    
    /**
     * 商户号
     */
    private String mchNo;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 应用密钥
     */
    private String appSecret;
    
    /**
     * 支付成功回调地址
     */
    private String notifyUrl;
    
    /**
     * 支付成功跳转地址
     */
    private String returnUrl;
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 30000;
}

// ==================== 游戏配置类 ====================
/**
 * 游戏相关配置
 * 文件位置: game-server/src/main/java/com/game/config/GameConfig.java
 */

@Configuration
@ConfigurationProperties(prefix = "game")
@Data
public class GameConfig {
    
    /**
     * 游戏币兑换比例（1元 = coinRate游戏币）
     */
    private int coinRate = 100;
    
    /**
     * 最小充值金额
     */
    private BigDecimal minRechargeAmount = new BigDecimal("1.00");
    
    /**
     * 最大充值金额
     */
    private BigDecimal maxRechargeAmount = new BigDecimal("10000.00");
    
    /**
     * 新用户初始游戏币
     */
    private BigDecimal initialCoinBalance = new BigDecimal("1000");
    
    /**
     * 是否启用充值优惠
     */
    private boolean enableRechargeBonus = false;
    
    /**
     * 充值优惠比例（百分比）
     */
    private BigDecimal rechargeBonusRate = new BigDecimal("0.05");
}

// ==================== 数据库实体类 ====================
/**
 * 游戏用户实体
 * 文件位置: game-server/src/main/java/com/game/entity/GameUser.java
 */

@Entity
@Table(name = "game_user")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GameUser {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 用户名（唯一）
     */
    @Column(name = "username", unique = true, nullable = false, length = 50)
    private String username;
    
    /**
     * 用户昵称
     */
    @Column(name = "nickname", length = 100)
    private String nickname;
    
    /**
     * 游戏币余额
     */
    @Column(name = "coin_balance", precision = 15, scale = 2, nullable = false)
    private BigDecimal coinBalance = BigDecimal.ZERO;
    
    /**
     * 累计充值金额
     */
    @Column(name = "total_recharge", precision = 15, scale = 2, nullable = false)
    private BigDecimal totalRecharge = BigDecimal.ZERO;
    
    /**
     * 用户状态：1-正常，0-禁用
     */
    @Column(name = "status", nullable = false)
    private Integer status = 1;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
    
    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
        updateTime = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }
    
    // 常量定义
    public static final int STATUS_NORMAL = 1;
    public static final int STATUS_DISABLED = 0;
}

/**
 * 充值订单实体
 * 文件位置: game-server/src/main/java/com/game/entity/RechargeOrder.java
 */

@Entity
@Table(name = "recharge_order")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RechargeOrder {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 游戏订单号（唯一）
     */
    @Column(name = "game_order_no", unique = true, nullable = false, length = 32)
    private String gameOrderNo;
    
    /**
     * 支付网关订单号
     */
    @Column(name = "pay_order_id", length = 32)
    private String payOrderId;
    
    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    /**
     * 用户名
     */
    @Column(name = "username", nullable = false, length = 50)
    private String username;
    
    /**
     * 充值金额（元）
     */
    @Column(name = "amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal amount;
    
    /**
     * 游戏币数量
     */
    @Column(name = "coin_amount", precision = 15, scale = 2, nullable = false)
    private BigDecimal coinAmount;
    
    /**
     * 支付方式
     */
    @Column(name = "pay_way", length = 20)
    private String payWay;
    
    /**
     * 订单状态：0-待支付，1-支付成功，2-支付失败，3-已取消
     */
    @Column(name = "status", nullable = false)
    private Integer status = STATUS_PENDING;
    
    /**
     * 支付时间
     */
    @Column(name = "pay_time")
    private LocalDateTime payTime;
    
    /**
     * 回调数据
     */
    @Column(name = "callback_data", columnDefinition = "TEXT")
    private String callbackData;
    
    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
    
    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
        updateTime = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }
    
    // 订单状态常量
    public static final int STATUS_PENDING = 0;   // 待支付
    public static final int STATUS_SUCCESS = 1;   // 支付成功
    public static final int STATUS_FAILED = 2;    // 支付失败
    public static final int STATUS_CANCELLED = 3; // 已取消
    
    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        switch (status) {
            case STATUS_PENDING: return "待支付";
            case STATUS_SUCCESS: return "支付成功";
            case STATUS_FAILED: return "支付失败";
            case STATUS_CANCELLED: return "已取消";
            default: return "未知状态";
        }
    }
    
    /**
     * 是否可以取消
     */
    public boolean canCancel() {
        return status == STATUS_PENDING;
    }
    
    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return status == STATUS_SUCCESS || status == STATUS_FAILED || status == STATUS_CANCELLED;
    }
}

// ==================== 数据访问层 ====================
/**
 * 游戏用户数据访问接口
 * 文件位置: game-server/src/main/java/com/game/repository/GameUserRepository.java
 */

@Repository
public interface GameUserRepository extends JpaRepository<GameUser, Long> {
    
    /**
     * 根据用户名查找用户
     */
    Optional<GameUser> findByUsername(String username);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 根据状态查找用户列表
     */
    List<GameUser> findByStatus(Integer status);
    
    /**
     * 查找游戏币余额大于指定值的用户
     */
    @Query("SELECT u FROM GameUser u WHERE u.coinBalance >= :minBalance ORDER BY u.coinBalance DESC")
    List<GameUser> findByMinCoinBalance(@Param("minBalance") BigDecimal minBalance);
    
    /**
     * 统计用户总数
     */
    @Query("SELECT COUNT(u) FROM GameUser u WHERE u.status = 1")
    long countActiveUsers();
    
    /**
     * 统计总游戏币数量
     */
    @Query("SELECT COALESCE(SUM(u.coinBalance), 0) FROM GameUser u WHERE u.status = 1")
    BigDecimal sumTotalCoins();
}

/**
 * 充值订单数据访问接口
 * 文件位置: game-server/src/main/java/com/game/repository/RechargeOrderRepository.java
 */

@Repository
public interface RechargeOrderRepository extends JpaRepository<RechargeOrder, Long> {
    
    /**
     * 根据游戏订单号查找订单
     */
    Optional<RechargeOrder> findByGameOrderNo(String gameOrderNo);
    
    /**
     * 根据支付网关订单号查找订单
     */
    Optional<RechargeOrder> findByPayOrderId(String payOrderId);
    
    /**
     * 根据用户名查找订单列表（按创建时间倒序）
     */
    List<RechargeOrder> findByUsernameOrderByCreateTimeDesc(String username);
    
    /**
     * 根据用户ID查找订单列表
     */
    List<RechargeOrder> findByUserIdOrderByCreateTimeDesc(Long userId);
    
    /**
     * 根据状态查找订单列表
     */
    List<RechargeOrder> findByStatus(Integer status);
    
    /**
     * 查找指定时间范围内的订单
     */
    @Query("SELECT o FROM RechargeOrder o WHERE o.createTime >= :startTime AND o.createTime <= :endTime ORDER BY o.createTime DESC")
    List<RechargeOrder> findByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计用户充值总金额
     */
    @Query("SELECT COALESCE(SUM(o.amount), 0) FROM RechargeOrder o WHERE o.username = :username AND o.status = 1")
    BigDecimal sumRechargeAmountByUsername(@Param("username") String username);
    
    /**
     * 统计指定状态的订单数量
     */
    long countByStatus(Integer status);
    
    /**
     * 查找超时未支付的订单
     */
    @Query("SELECT o FROM RechargeOrder o WHERE o.status = 0 AND o.createTime < :expireTime")
    List<RechargeOrder> findExpiredOrders(@Param("expireTime") LocalDateTime expireTime);
}

// ==================== 应用配置文件 ====================
/**
 * application.yml 配置示例
 * 文件位置: game-server/src/main/resources/application.yml
 */

/*
server:
  port: 8088
  servlet:
    context-path: /

spring:
  application:
    name: game-server
  
  # 数据库配置
  datasource:
    url: jdbc:h2:mem:gamedb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
  
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Thymeleaf配置
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html

# 支付网关配置
payment:
  gateway-url: http://*************:9216
  mch-no: M1757512463
  app-id: 68c1830fd138c62623a963c0
  app-secret: 1234567890abcdef1234567890abcdef
  notify-url: http://*************:8088/api/payment/notify
  return-url: http://*************:8088/recharge/success
  connect-timeout: 30000
  read-timeout: 30000

# 游戏配置
game:
  coin-rate: 100
  min-recharge-amount: 1.00
  max-recharge-amount: 10000.00
  initial-coin-balance: 1000
  enable-recharge-bonus: false
  recharge-bonus-rate: 0.05

# 日志配置
logging:
  level:
    com.game: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p %pid --- [%15.15t] %-40.40logger{39} : %m%n"
*/