/**
 * 支付订单创建实现示例
 * 文件位置: game-server/src/main/java/com/game/service/RechargeService.java
 */

@Slf4j
@Service
public class RechargeService {

    @Autowired
    private RechargeOrderRepository rechargeOrderRepository;

    @Autowired
    private GameUserService gameUserService;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private GameConfig gameConfig;

    /**
     * 创建充值订单 - 核心订单创建方法
     * 
     * @param username 用户名
     * @param amount 充值金额
     * @return 包含订单信息和二维码URL的JSON对象
     */
    @Transactional
    public JSONObject createRechargeOrder(String username, BigDecimal amount) {
        
        // 1. 获取或创建用户
        GameUser user = gameUserService.getOrCreateUser(username);
        log.info("用户信息: ID={}, 用户名={}, 当前余额={}", user.getId(), user.getUsername(), user.getCoinBalance());

        // 2. 计算游戏币数量 (1元 = 100游戏币)
        BigDecimal coinAmount = amount.multiply(new BigDecimal(gameConfig.getCoinRate()));
        log.info("充值计算: {}元 = {}游戏币", amount, coinAmount);

        // 3. 创建充值订单实体
        RechargeOrder order = new RechargeOrder();
        order.setGameOrderNo(PaymentUtil.generateOrderNo());
        order.setUserId(user.getId());
        order.setUsername(username);
        order.setAmount(amount);
        order.setCoinAmount(coinAmount);
        order.setStatus(RechargeOrder.STATUS_PENDING);
        order.setPayWay("ALI_QR");
        order.setRemark("支付宝二维码充值");

        // 4. 保存订单到数据库
        order = rechargeOrderRepository.save(order);
        log.info("订单保存成功: 订单号={}, 用户={}, 金额={}", order.getGameOrderNo(), username, amount);

        // 5. 调用支付网关创建支付订单
        try {
            String subject = "游戏充值 - " + coinAmount + "游戏币";
            String body = String.format("用户 %s 充值 %s元，获得 %s 游戏币", username, amount, coinAmount);
            
            // 调用支付服务创建支付订单
            JSONObject payResult = paymentService.createPayOrder(order.getGameOrderNo(), amount, subject, body);
            
            if (payResult.getInteger("code") == 0) {
                // 支付订单创建成功
                JSONObject data = payResult.getJSONObject("data");
                String payOrderId = data.getString("payOrderId");
                String qrCodeUrl = data.getString("payData");
                
                // 更新订单的支付网关订单号
                order.setPayOrderId(payOrderId);
                order = rechargeOrderRepository.save(order);
                
                log.info("支付订单创建成功: 游戏订单号={}, 支付订单号={}, 二维码URL={}", 
                        order.getGameOrderNo(), payOrderId, qrCodeUrl);
                
                // 6. 构造返回结果
                JSONObject result = new JSONObject();
                result.put("id", order.getId());
                result.put("gameOrderNo", order.getGameOrderNo());
                result.put("payOrderId", order.getPayOrderId());
                result.put("userId", order.getUserId());
                result.put("username", order.getUsername());
                result.put("amount", order.getAmount());
                result.put("coinAmount", order.getCoinAmount());
                result.put("status", order.getStatus());
                result.put("payWay", order.getPayWay());
                result.put("qrCodeUrl", qrCodeUrl); // 关键：二维码URL
                result.put("createTime", order.getCreateTime());
                
                return result;
                
            } else {
                // 支付订单创建失败
                String errorMsg = payResult.getString("msg");
                log.error("支付订单创建失败: 订单号={}, 错误信息={}", order.getGameOrderNo(), errorMsg);
                
                // 更新订单状态为失败
                order.setStatus(RechargeOrder.STATUS_FAILED);
                order.setRemark("支付订单创建失败: " + errorMsg);
                rechargeOrderRepository.save(order);
                
                throw new RuntimeException("支付订单创建失败: " + errorMsg);
            }
            
        } catch (Exception e) {
            log.error("创建支付订单异常: 订单号={}", order.getGameOrderNo(), e);
            
            // 更新订单状态为失败
            order.setStatus(RechargeOrder.STATUS_FAILED);
            order.setRemark("系统异常: " + e.getMessage());
            rechargeOrderRepository.save(order);
            
            throw new RuntimeException("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单号获取订单
     */
    public RechargeOrder getOrderByGameOrderNo(String gameOrderNo) {
        return rechargeOrderRepository.findByGameOrderNo(gameOrderNo).orElse(null);
    }

    /**
     * 根据用户名获取订单列表
     */
    public List<RechargeOrder> getOrdersByUsername(String username) {
        return rechargeOrderRepository.findByUsernameOrderByCreateTimeDesc(username);
    }

    /**
     * 查询支付订单状态
     */
    public JSONObject queryPayOrderStatus(String gameOrderNo) {
        RechargeOrder order = getOrderByGameOrderNo(gameOrderNo);
        if (order == null || order.getPayOrderId() == null) {
            return null;
        }
        return paymentService.queryPayOrder(order.getPayOrderId());
    }
}

/**
 * 支付网关接口调用实现
 * 文件位置: game-server/src/main/java/com/game/service/PaymentService.java
 */

@Slf4j
@Service
public class PaymentService {

    @Autowired
    private PaymentConfig paymentConfig;

    /**
     * 创建支付订单
     * 
     * @param mchOrderNo 商户订单号
     * @param amount 支付金额
     * @param subject 订单标题
     * @param body 订单描述
     * @return 支付网关响应
     */
    public JSONObject createPayOrder(String mchOrderNo, BigDecimal amount, String subject, String body) {
        try {
            // 1. 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("mchNo", paymentConfig.getMchNo());
            params.put("appId", paymentConfig.getAppId());
            params.put("mchOrderNo", mchOrderNo);
            params.put("wayCode", "ALI_QR"); // 支付宝二维码
            params.put("amount", amount.multiply(new BigDecimal(100)).longValue()); // 转换为分
            params.put("currency", "cny");
            params.put("clientIp", "127.0.0.1");
            params.put("subject", subject);
            params.put("body", body);
            params.put("notifyUrl", paymentConfig.getNotifyUrl());
            params.put("returnUrl", paymentConfig.getReturnUrl());
            params.put("reqTime", System.currentTimeMillis());
            params.put("version", "1.0");
            params.put("signType", "MD5");
            params.put("channelExtra", "{}");
            params.put("extParam", "{}");

            // 2. 生成签名
            String sign = PaymentUtil.generateSign(params, paymentConfig.getAppSecret());
            params.put("sign", sign);

            log.info("支付订单创建请求: {}", JSON.toJSONString(params));

            // 3. 发送HTTP请求到支付网关
            String requestUrl = paymentConfig.getGatewayUrl() + "/api/pay/unifiedOrder";
            String response = sendHttpRequest(requestUrl, params);
            
            log.info("支付订单创建响应: {}", response);
            
            // 4. 解析响应
            JSONObject result = JSON.parseObject(response);
            
            // 5. 验证响应签名
            if (result.getInteger("code") == 0) {
                JSONObject data = result.getJSONObject("data");
                String responseSign = result.getString("sign");
                
                // 验证响应签名
                Map<String, Object> responseParams = new HashMap<>();
                responseParams.put("code", result.get("code"));
                responseParams.put("msg", result.get("msg"));
                responseParams.put("data", data);
                
                boolean signValid = PaymentUtil.verifySign(responseParams, paymentConfig.getAppSecret(), responseSign);
                if (!signValid) {
                    log.error("支付网关响应签名验证失败");
                    throw new RuntimeException("支付网关响应签名验证失败");
                }
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("创建支付订单异常", e);
            throw new RuntimeException("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 发送HTTP请求
     */
    private String sendHttpRequest(String url, Map<String, Object> params) throws Exception {
        // 使用Apache HttpClient或其他HTTP客户端发送请求
        // 这里简化实现，实际项目中需要完整的HTTP客户端实现
        
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        
        // 设置请求头
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("User-Agent", "GameServer/1.0");
        
        // 设置请求体
        String jsonParams = JSON.toJSONString(params);
        StringEntity entity = new StringEntity(jsonParams, "UTF-8");
        httpPost.setEntity(entity);
        
        // 执行请求
        CloseableHttpResponse response = httpClient.execute(httpPost);
        
        try {
            HttpEntity responseEntity = response.getEntity();
            String responseString = EntityUtils.toString(responseEntity, "UTF-8");
            
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                throw new RuntimeException("HTTP请求失败，状态码: " + statusCode);
            }
            
            return responseString;
            
        } finally {
            response.close();
            httpClient.close();
        }
    }

    /**
     * 查询支付订单状态
     */
    public JSONObject queryPayOrder(String payOrderId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("mchNo", paymentConfig.getMchNo());
            params.put("appId", paymentConfig.getAppId());
            params.put("payOrderId", payOrderId);
            params.put("reqTime", System.currentTimeMillis());
            params.put("version", "1.0");
            params.put("signType", "MD5");

            String sign = PaymentUtil.generateSign(params, paymentConfig.getAppSecret());
            params.put("sign", sign);

            String requestUrl = paymentConfig.getGatewayUrl() + "/api/pay/query";
            String response = sendHttpRequest(requestUrl, params);
            
            return JSON.parseObject(response);
            
        } catch (Exception e) {
            log.error("查询支付订单异常", e);
            return null;
        }
    }
}