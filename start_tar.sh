#!/bin/bash

# 脚本功能：启动target目录下的jar包
# 作者：Assistant
# 日期：$(date +%Y-%m-%d)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
MODULE_NAME=""
PROFILE="dev" # 默认环境配置文件
JAVA_OPTS="-Xms512m -Xmx1024m"
SERVER_PORT="" # 服务绑定端口
CONFIG_LOCATION="" # 自定义配置文件路径
JAR_FILE=""
LOG_PATH=""
ACTION="start"  # 默认动作为启动
TAIL_LINES=20   # 默认显示日志行数
FOLLOW_LOG=false # 是否实时跟踪日志
FOLLOW_ON_START=false # 启动后是否跟踪日志

# 支持的模块映射
declare -A MODULE_MAP
MODULE_MAP["0"]="sys-payment"
MODULE_MAP["1"]="sys-manager"
MODULE_MAP["2"]="sys-merchant"
MODULE_MAP["3"]="sys-agent"
MODULE_MAP["payment"]="sys-payment"
MODULE_MAP["manager"]="sys-manager"
MODULE_MAP["merchant"]="sys-merchant"
MODULE_MAP["agent"]="sys-agent"
MODULE_MAP["sys-payment"]="sys-payment"
MODULE_MAP["sys-manager"]="sys-manager"
MODULE_MAP["sys-merchant"]="sys-merchant"
MODULE_MAP["sys-agent"]="sys-agent"

# 所有支持的模块列表
ALL_MODULES=("sys-payment" "sys-manager" "sys-merchant" "sys-agent")

# 打印使用说明
usage() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -m, --module MODULE     指定模块名称 (payment, manager, merchant, agent)"
    echo "                          或使用完整名称 (sys-payment, sys-manager, sys-merchant, sys-agent)"
    echo "  -p, --profile PROFILE   指定运行环境 (dev, test, prod) 默认: dev"
    echo "  -P, --port PORT         指定服务绑定端口"
    echo "  -c, --config PATH       指定自定义配置文件路径"
    echo "  -o, --options JAVA_OPTS 指定JVM参数 默认: -Xms512m -Xmx1024m"
    echo "  -k, --kill-all          杀掉所有正在运行的UniPay服务"
    echo "  -x, --kill-module MODULE  杀掉指定的单个UniPay服务模块"
    echo "  -s, --status            查看所有UniPay服务的运行状态"
    echo "  -l, --log [LINES]       显示指定模块的日志，默认显示最后20行"
    echo "  -f, --follow            实时跟踪日志输出（需要与--log配合使用）"
    echo "  -F, --follow-on-start   启动服务后实时跟踪日志输出"
    echo "  -h, --help              显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -m payment                    # 启动支付网关"
    echo "  $0 --module manager --profile prod  # 生产环境启动运营平台"
    echo "  $0 -m payment -P 8080           # 指定端口8080启动支付网关"
    echo "  $0 -m payment -c /path/to/config.yml # 指定自定义配置文件路径启动支付网关"
    echo "  $0 -k                            # 杀掉所有服务"
    echo "  $0 --status                      # 查看所有服务状态"
    echo "  $0 -m payment --log              # 显示支付网关最后20行日志"
    echo "  $0 -m payment --log 50           # 显示支付网关最后50行日志"
    echo "  $0 -m payment --log --follow     # 实时跟踪支付网关日志"
    echo "  $0 -m payment -F                 # 启动支付网关并实时跟踪日志"
    exit 1
}

# 杀掉指定模块的进程
# 杀掉单个模块的进程
kill_module() {
    local module=$1
    local pid_file="$module/target/app.pid"

    # 优先使用 pgrep -f 精确匹配 jar 名称；若无 pgrep 则回退到 ps+awk 的稳健解析
    local pids_by_name=""
    if command -v pgrep >/dev/null 2>&1; then
        pids_by_name=$(pgrep -f "java .*${module}\.jar")
    else
        # 在 ps 输出中寻找第一个纯数字字段作为 PID，避免把用户名当作 PID
        pids_by_name=$(ps -ef | grep "java" | grep "${module}\.jar" | grep -v "grep" | awk '{for(i=1;i<=NF;i++){if($i ~ /^[0-9]+$/){print $i; break}}}')
    fi

    if [ -n "$pids_by_name" ]; then
        # 将换行转为空格用于展示
        local display_pids=$(echo "$pids_by_name" | tr '\n' ' ' | sed 's/ *$//')
        echo -e "${YELLOW}找到以下进程 PID: $display_pids，正在终止 $module...${NC}"
        # 逐个 kill，提升兼容性与稳定性
        for pid in $pids_by_name; do
            kill "$pid" 2>/dev/null || true
        done
        sleep 2
        # 强杀仍存活的
        local still_alive=()
        for pid in $pids_by_name; do
            if ps -p "$pid" >/dev/null 2>&1; then
                still_alive+=("$pid")
            fi
        done
        if [ ${#still_alive[@]} -gt 0 ]; then
            echo -e "${RED}进程未正常结束，强制终止: ${still_alive[*]}${NC}"
            for pid in "${still_alive[@]}"; do
                kill -9 "$pid" 2>/dev/null || true
            done
        fi
        rm -f "$pid_file" # 无论如何都尝试删除PID文件
        echo -e "${GREEN}$module 已终止${NC}"
    else
        echo -e "${YELLOW}$module 没有运行或未找到相关进程${NC}"
        rm -f "$pid_file" # 尝试删除可能存在的无效PID文件
    fi
}

# 杀掉所有模块的进程
kill_all_modules() {
    echo -e "${BLUE}准备终止所有UniPay服务...${NC}"
    
    # 查找正在运行的服务
    local running_modules=()
    local running_pids_info=() # 保存展示信息

    for module in "${ALL_MODULES[@]}"; do
        local pids_by_name=""
        if command -v pgrep >/dev/null 2>&1; then
            pids_by_name=$(pgrep -f "java .*${module}\.jar")
        else
            pids_by_name=$(ps -ef | grep "java" | grep "${module}\.jar" | grep -v "grep" | awk '{for(i=1;i<=NF;i++){ if($i ~ /^[0-9]+$/){print $i; break}}}')
        fi
        if [ -n "$pids_by_name" ]; then
            running_modules+=("$module")
            local one_line_pids=$(echo "$pids_by_name" | tr '\n' ' ' | sed 's/ *$//')
            running_pids_info+=("$module (PID: $one_line_pids)")
        fi
    done

    if [ ${#running_modules[@]} -eq 0 ]; then
        echo -e "${YELLOW}没有正在运行的UniPay服务${NC}"
        return 0
    fi

    echo -e "${BLUE}将要终止以下服务:${NC}"
    for info in "${running_pids_info[@]}"; do
        echo -e "  $info"
    done
    
    # 确认操作
    echo -e "${YELLOW}是否确认终止以上服务? (y/N)${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}取消终止操作${NC}"
        return 0
    fi
    
    # 逐个模块调用 kill_module
    for module in "${running_modules[@]}"; do
        kill_module "$module"
    done
    
    echo -e "${GREEN}所有服务终止操作已完成${NC}"
}

# 显示所有模块的状态
show_status() {
    echo -e "${BLUE}UniPay服务状态:${NC}"
    echo "----------------------------------------"
    for module in "${ALL_MODULES[@]}"; do
        if [ -d "$module" ]; then
            if [ -f "$module/target/app.pid" ]; then
                local pid=$(cat "$module/target/app.pid")
                if ps -p "$pid" > /dev/null 2>&1; then
                    echo -e "$module: ${GREEN}运行中 (PID: $pid)${NC}"
                else
                    # 当 PID 文件无效时，按进程名精确匹配
                    local pids_by_name=""
                    if command -v pgrep >/dev/null 2>&1; then
                        pids_by_name=$(pgrep -f "java .*${module}\.jar")
                    else
                        pids_by_name=$(ps -ef | grep "java" | grep "${module}\.jar" | grep -v "grep" | awk '{for(i=1;i<=NF;i++){ if($i ~ /^[0-9]+$/){print $i; break}}}')
                    fi
                    if [ -n "$pids_by_name" ]; then
                        local display_pids=$(echo "$pids_by_name" | tr '\n' ' ' | sed 's/ *$//')
                        echo -e "$module: ${GREEN}运行中 (PID文件损坏，实际PID: $display_pids)${NC}"
                    else
                        echo -e "$module: ${RED}PID文件存在但进程不存在${NC}"
                    fi
                fi
            else
                # 未有 PID 文件时也尝试按进程名判断
                local pids_by_name=""
                if command -v pgrep >/dev/null 2>&1; then
                    pids_by_name=$(pgrep -f "java .*${module}\.jar")
                else
                    pids_by_name=$(ps -ef | grep "java" | grep "${module}\.jar" | grep -v "grep" | awk '{for(i=1;i<=NF;i++){ if($i ~ /^[0-9]+$/){print $i; break}}}')
                fi
                if [ -n "$pids_by_name" ]; then
                    local display_pids=$(echo "$pids_by_name" | tr '\n' ' ' | sed 's/ *$//')
                    echo -e "$module: ${GREEN}运行中 (未生成PID文件，实际PID: $display_pids)${NC}"
                else
                    echo -e "$module: ${YELLOW}未运行${NC}"
                fi
            fi
        else
            echo -e "$module: ${RED}目录不存在${NC}"
        fi
    done
    echo "----------------------------------------"
}

# 显示指定模块的日志
show_log() {
    local module=$1
    local lines=$2
    local follow=$3
    
    # 获取实际模块目录名
    local real_module=""
    if [ -n "${MODULE_MAP[$module]}" ]; then
        real_module="${MODULE_MAP[$module]}"
    else
        echo -e "${RED}错误: 不支持的模块名称 '$module'${NC}"
        echo "支持的模块: payment, manager, merchant, agent"
        echo "或使用完整名称: sys-payment, sys-manager, sys-merchant, sys-agent"
        exit 1
    fi
    
    # 检查模块目录是否存在
    if [ ! -d "$real_module" ]; then
        echo -e "${RED}错误: 模块目录 $real_module 不存在${NC}"
        exit 1
    fi
    
    # 检查日志文件
    local log_file="$real_module/target/logs/startup.log"
    if [ ! -f "$log_file" ]; then
        echo -e "${RED}错误: 日志文件 $log_file 不存在${NC}"
        exit 1
    fi
    
    if [ "$follow" = true ]; then
        echo -e "${BLUE}实时跟踪 $real_module 模块日志输出 (按 Ctrl+C 退出):${NC}"
        echo "=================================================="
        tail -f "$log_file"
    else
        echo -e "${BLUE}显示 $real_module 模块最后 $lines 行日志:${NC}"
        echo "=================================================="
        tail -n "$lines" "$log_file"
        echo "=================================================="
    fi
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--module)
            MODULE_NAME="$2"
            ACTION="start"
            shift 2
            ;;
        -p|--profile)
            PROFILE="$2"
            shift 2
            ;;
        -P|--port)
            SERVER_PORT="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG_LOCATION="$2"
            shift 2
            ;;
        -o|--options)
            JAVA_OPTS="$2"
            shift 2
            ;;
        -k|--kill-all)
            ACTION="kill"
            shift
            ;;
        -x|--kill-module)
            ACTION="kill_single"
            if [ -z "$2" ]; then
                echo -e "${RED}错误: --kill-module 选项需要指定模块名称${NC}"
                usage
            fi
            MODULE_NAME=$2
            shift 2
            ;;
        -s|--status)
            ACTION="status"
            shift
            ;;
        -l|--log)
            ACTION="log"
            # 检查是否指定了行数
            if [[ $# -gt 1 && $2 =~ ^[0-9]+$ ]]; then
                TAIL_LINES=$2
                shift 2
            else
                shift
            fi
            ;;
        -f|--follow)
            FOLLOW_LOG=true
            shift
            ;;
        -F|--follow-on-start)
            FOLLOW_ON_START=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            usage
            ;;
    esac
done

# 根据动作执行相应操作
case "$ACTION" in
    "kill")
        kill_all_modules
        exit 0
        ;;
    "kill_single")
        if [ -z "$MODULE_NAME" ]; then
            echo -e "${RED}错误: 杀掉单个模块需要指定模块名称${NC}"
            usage
        fi
        kill_module "$MODULE_NAME"
        exit 0
        ;;
    "status")
        show_status
        exit 0
        ;;
    "log")
        if [ -z "$MODULE_NAME" ]; then
            echo -e "${RED}错误: 显示日志需要指定模块名称${NC}"
            usage
        fi
        show_log "$MODULE_NAME" "$TAIL_LINES" "$FOLLOW_LOG"
        exit 0
        ;;
    "start")
        # 继续执行启动逻辑
        ;;
esac

# 检查模块名称
if [ -z "$MODULE_NAME" ]; then
    echo -e "${RED}错误: 请指定模块名称${NC}"
    usage
fi

# 获取实际模块目录名
if [ -n "${MODULE_MAP[$MODULE_NAME]}" ]; then
    REAL_MODULE_NAME="${MODULE_MAP[$MODULE_NAME]}"
else
    echo -e "${RED}错误: 不支持的模块名称 '$MODULE_NAME'${NC}"
    echo "支持的模块: payment, manager, merchant, agent"
    echo "或使用完整名称: sys-payment, sys-manager, sys-merchant, sys-agent"
    exit 1
fi

# 检查模块目录是否存在
if [ ! -d "$REAL_MODULE_NAME" ]; then
    echo -e "${RED}错误: 模块目录 $REAL_MODULE_NAME 不存在${NC}"
    exit 1
fi

# 检查target目录
TARGET_DIR="$REAL_MODULE_NAME/target"
if [ ! -d "$TARGET_DIR" ]; then
    echo -e "${RED}错误: $TARGET_DIR 目录不存在，请先构建项目${NC}"
    exit 1
fi

# 查找jar文件
JAR_FILE=$(find "$TARGET_DIR" -name "*.jar" -not -name "*original*" | head -n 1)
if [ -z "$JAR_FILE" ]; then
    echo -e "${RED}错误: 在 $TARGET_DIR 目录中未找到jar文件${NC}"
    exit 1
fi

# 设置日志路径
LOG_PATH="$TARGET_DIR/logs"
mkdir -p "$LOG_PATH"

# 构建启动命令
START_CMD="java $JAVA_OPTS -jar $JAR_FILE --spring.profiles.active=$PROFILE"

# 如果指定了端口，则添加端口配置
if [ -n "$SERVER_PORT" ]; then
    START_CMD="$START_CMD --server.port=$SERVER_PORT"
fi

# 如果指定了自定义配置文件路径，则添加配置
if [ -n "$CONFIG_LOCATION" ]; then
    START_CMD="$START_CMD --spring.config.location=$CONFIG_LOCATION"
fi

# 显示启动信息
echo -e "${GREEN}=================================${NC}"
echo -e "${GREEN}准备启动UniPay $REAL_MODULE_NAME 模块${NC}"
echo -e "${GREEN}=================================${NC}"
echo "模块名称: $REAL_MODULE_NAME (通过 $MODULE_NAME 指定)"
echo "运行环境: $PROFILE"
echo "JVM参数: $JAVA_OPTS"
if [ -n "$SERVER_PORT" ]; then
    echo "服务端口: $SERVER_PORT"
fi
if [ -n "$CONFIG_LOCATION" ]; then
    echo "配置文件路径: $CONFIG_LOCATION"
fi
echo "JAR文件: $JAR_FILE"
echo "日志路径: $LOG_PATH"
echo "启动命令: $START_CMD"
echo -e "${GREEN}=================================${NC}"

# 确认启动，无输入则直接启动
echo -e "${YELLOW}是否确认启动? (Y/n)${NC}"
read -r confirm

# 如果未输入任何内容（直接回车），或输入Y/y，则继续启动
if [[ -z "$confirm" || "$confirm" =~ ^[Yy]$ ]]; then
    echo -e "${GREEN}开始启动...${NC}"
else
    echo -e "${YELLOW}取消启动${NC}"
    exit 0
fi

# 杀掉已存在的进程
kill_module "$REAL_MODULE_NAME"

# 启动应用
echo -e "${GREEN}正在启动应用...${NC}"
nohup $START_CMD > "$LOG_PATH/startup.log" 2>&1 &
PID=$!

# 保存PID到文件
echo $PID > "$TARGET_DIR/app.pid"

echo -e "${GREEN}应用已在后台启动，PID: $PID${NC}"
echo -e "${GREEN}日志文件: $LOG_PATH/startup.log${NC}"
echo -e "${GREEN}PID文件: $TARGET_DIR/app.pid${NC}"
echo -e "${GREEN}启动完成!${NC}"

# 如果指定了启动后跟踪日志，则执行跟踪
if [ "$FOLLOW_ON_START" = true ]; then
    echo -e "${BLUE}启动后实时跟踪日志输出 (按 Ctrl+C 退出):${NC}"
    echo "=================================================="
    tail -f "$LOG_PATH/startup.log"
fi