# UniPay Portal 宝塔面板部署指南

## 🎯 概述

本指南专门针对使用宝塔面板的服务器环境，提供详细的部署步骤和配置说明。

## 📋 前置要求

### 服务器环境
- ✅ 已安装宝塔面板 (7.x 或更高版本)
- ✅ 已安装Nginx (通过宝塔面板)
- ✅ 服务器系统: CentOS 7+ 或 Ubuntu 18.04+
- ✅ 域名 `ybdl.shop` 已解析到服务器IP

### 宝塔面板安装
如果还未安装宝塔面板，请执行：

```bash
# CentOS/RHEL
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

## 🚀 快速部署步骤

### 第一步：上传文件到服务器

```bash
# 方法1: 使用SCP上传
scp -r unipay-portal/ root@your-server:/tmp/

# 方法2: 使用宝塔面板文件管理器
# 登录宝塔面板 → 文件 → 上传文件到 /tmp/unipay-portal/
```

### 第二步：在宝塔面板中创建站点

1. 登录宝塔面板 (http://服务器IP:8888)
2. 进入 **网站** 管理
3. 点击 **添加站点**
4. 填写信息：
   - **域名**: `ybdl.shop`
   - **根目录**: `/www/wwwroot/ybdl.shop` (自动生成)
   - **PHP版本**: 纯静态 (或选择任意版本)
   - **数据库**: 不创建
5. 点击 **提交**

### 第三步：运行部署脚本

```bash
# 进入项目目录
cd /tmp/unipay-portal

# 给脚本执行权限
chmod +x bt-deploy.sh

# 运行宝塔部署脚本
./bt-deploy.sh
```

### 第四步：配置Nginx反向代理

1. 在宝塔面板中找到站点 `ybdl.shop`
2. 点击 **设置**
3. 选择 **配置文件** 选项卡
4. 将 `bt-nginx.conf` 文件内容复制到配置文件中
5. 点击 **保存**

### 第五步：开放端口

在宝塔面板中开放以下端口：

1. 进入 **安全** 管理
2. 添加端口规则：
   - `9216` - 支付网关
   - `9217` - 运营平台  
   - `9218` - 商户平台
   - `9219` - 代理商平台

### 第六步：配置SSL证书 (可选)

1. 在站点设置中选择 **SSL** 选项卡
2. 选择 **Let's Encrypt** 免费证书
3. 填写域名: `ybdl.shop`
4. 点击 **申请**
5. 开启 **强制HTTPS**

## 📁 文件结构说明

```
/www/wwwroot/ybdl.shop/          # 站点根目录
├── index.html                   # 门户首页
├── styles.css                   # 样式文件
├── script.js                    # JavaScript文件
├── 404.html                     # 404错误页
├── 50x.html                     # 服务器错误页
├── monitor-bt.sh                # 监控脚本
└── *.md                         # 文档文件
```

## 🔧 详细配置说明

### Nginx配置要点

宝塔面板的nginx配置文件位置：
- 主配置: `/www/server/nginx/conf/nginx.conf`
- 站点配置: `/www/server/panel/vhost/nginx/ybdl.shop.conf`

关键配置内容：

```nginx
# 反向代理配置
location /manager {
    proxy_pass http://127.0.0.1:9217;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

location /agent {
    proxy_pass http://127.0.0.1:9219;
    # ... 其他配置
}

location /merchant {
    proxy_pass http://127.0.0.1:9218;
    # ... 其他配置
}
```

### 端口开放说明

| 端口 | 服务 | 说明 |
|------|------|------|
| 80 | HTTP | 网站访问端口 |
| 443 | HTTPS | SSL加密访问端口 |
| 8888 | 宝塔面板 | 面板管理端口 |
| 9216 | 支付网关 | UniPay支付服务 |
| 9217 | 运营平台 | 系统管理后台 |
| 9218 | 商户平台 | 商户管理后台 |
| 9219 | 代理商平台 | 代理商管理后台 |

## 🔍 监控和维护

### 使用监控脚本

```bash
# 检查服务状态
/www/wwwroot/ybdl.shop/monitor-bt.sh

# 查看监控日志
tail -f /www/wwwlogs/unipay-monitor.log
```

### 宝塔面板监控

1. **系统监控**: 面板首页查看CPU、内存、磁盘使用情况
2. **网站监控**: 网站管理中查看访问统计
3. **日志监控**: 查看nginx访问日志和错误日志

### 日志文件位置

- **Nginx访问日志**: `/www/wwwlogs/ybdl.shop.log`
- **Nginx错误日志**: `/www/wwwlogs/ybdl.shop.error.log`
- **宝塔面板日志**: `/www/server/panel/logs/`
- **UniPay监控日志**: `/www/wwwlogs/unipay-monitor.log`

## 🛠️ 故障排除

### 常见问题

#### 1. 网站无法访问
```bash
# 检查nginx状态
systemctl status nginx

# 重启nginx
systemctl restart nginx

# 或在宝塔面板中重启nginx服务
```

#### 2. 反向代理不工作
- 检查后端服务是否运行在对应端口
- 确认防火墙端口已开放
- 检查nginx配置文件语法

#### 3. SSL证书问题
- 确认域名解析正确
- 检查80端口是否被占用
- 在宝塔面板SSL管理中重新申请

#### 4. 宝塔面板无法访问
```bash
# 重启宝塔面板
bt restart

# 查看面板状态
bt status

# 修改面板端口
bt port 8888
```

### 性能优化

#### 1. 启用缓存
在宝塔面板中：
- 网站设置 → 性能优化 → 开启Gzip压缩
- 网站设置 → 性能优化 → 开启静态文件缓存

#### 2. 配置CDN
- 使用宝塔面板的CDN加速功能
- 或配置第三方CDN服务

#### 3. 数据库优化
- 定期清理日志文件
- 优化MySQL配置 (如果使用数据库)

## 🔒 安全配置

### 宝塔面板安全

1. **修改默认端口**:
   ```bash
   bt port 新端口号
   ```

2. **设置面板密码**:
   ```bash
   bt password 新密码
   ```

3. **绑定域名访问**:
   ```bash
   bt domain 域名
   ```

4. **开启BasicAuth**:
   在面板设置中启用BasicAuth认证

### 网站安全

1. **防火墙配置**: 只开放必要端口
2. **SSL证书**: 强制HTTPS访问
3. **访问限制**: 配置IP白名单 (如需要)
4. **定期备份**: 使用宝塔面板的自动备份功能

## 📊 备份策略

### 自动备份设置

1. 进入宝塔面板 → **计划任务**
2. 添加备份任务：
   - **任务类型**: 备份网站
   - **执行周期**: 每天凌晨2点
   - **备份到**: 本地磁盘 + 云存储
   - **保留份数**: 7份

### 手动备份

```bash
# 备份网站文件
tar -czf /tmp/unipay-backup-$(date +%Y%m%d).tar.gz /www/wwwroot/ybdl.shop/

# 备份nginx配置
cp /www/server/panel/vhost/nginx/ybdl.shop.conf /tmp/nginx-backup-$(date +%Y%m%d).conf
```

## 📞 技术支持

### 宝塔面板相关
- 官方文档: https://www.bt.cn/bbs/
- 在线客服: 宝塔面板内置客服系统

### UniPay相关
- 查看项目文档: `README.md`
- 运行监控脚本: `monitor-bt.sh`
- 查看部署日志: `/www/wwwlogs/unipay-monitor.log`

---

**部署完成后访问地址：**
- 🏠 门户首页: https://ybdl.shop
- 🏢 运营平台: https://ybdl.shop/manager  
- 👥 代理商平台: https://ybdl.shop/agent
- 🏪 商户平台: https://ybdl.shop/merchant
- ⚙️ 宝塔面板: http://服务器IP:8888