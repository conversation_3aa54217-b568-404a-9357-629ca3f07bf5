# UniPay 单域名部署配置
# 适用于开发/测试环境，所有服务通过路径区分

upstream unipay_manager {
    server 127.0.0.1:8080;
}

upstream unipay_payment {
    server 127.0.0.1:9216;
}

upstream unipay_merchant {
    server 127.0.0.1:9218;
}

upstream unipay_agent {
    server 127.0.0.1:9219;
}

upstream unipay_game {
    server 127.0.0.1:8088;
}

server {
    listen 80;
    server_name unipay.local;  # 或者你的域名
    
    # 日志配置
    access_log /var/log/nginx/unipay-access.log;
    error_log /var/log/nginx/unipay-error.log;
    
    # 全局安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 管理后台 - /manager/
    location /manager/ {
        rewrite ^/manager/(.*)$ /$1 break;
        proxy_pass http://unipay_manager;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 商户平台 - /merchant/
    location /merchant/ {
        rewrite ^/merchant/(.*)$ /$1 break;
        proxy_pass http://unipay_merchant;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 代理商平台 - /agent/
    location /agent/ {
        rewrite ^/agent/(.*)$ /$1 break;
        proxy_pass http://unipay_agent;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 支付网关 - /pay/
    location /pay/ {
        # 支付接口限流
        limit_req_zone $binary_remote_addr zone=payment:10m rate=10r/s;
        limit_req zone=payment burst=20 nodelay;
        
        rewrite ^/pay/(.*)$ /$1 break;
        proxy_pass http://unipay_payment;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 游戏服务 - /game/
    location /game/ {
        rewrite ^/game/(.*)$ /$1 break;
        proxy_pass http://unipay_game;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 默认路由到管理后台
    location / {
        proxy_pass http://unipay_manager;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}