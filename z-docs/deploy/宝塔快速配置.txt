UniPay Portal 宝塔面板快速配置指南
==========================================

🚀 一键部署命令：
cd /tmp && wget https://your-server.com/unipay-portal.zip && unzip unipay-portal.zip && cd unipay-portal && chmod +x bt-deploy.sh && ./bt-deploy.sh

📋 手动配置步骤：

1️⃣ 创建站点
   - 登录宝塔面板: http://服务器IP:8888
   - 网站 → 添加站点
   - 域名: ybdl.shop
   - 根目录: /www/wwwroot/ybdl.shop
   - PHP版本: 纯静态

2️⃣ 上传文件
   - 文件管理 → 进入 /www/wwwroot/ybdl.shop/
   - 上传: index.html, styles.css, script.js, 404.html, 50x.html

3️⃣ 配置Nginx
   - 网站 → ybdl.shop → 设置 → 配置文件
   - 复制 bt-nginx.conf 内容到配置文件
   - 保存并重载

4️⃣ 开放端口
   - 安全 → 添加端口规则
   - 端口: 9216, 9217, 9218, 9219
   - 说明: UniPay服务端口

5️⃣ 配置SSL (可选)
   - 网站 → ybdl.shop → 设置 → SSL
   - Let's Encrypt → 申请证书
   - 开启强制HTTPS

🔧 关键配置内容：

Nginx反向代理配置：
```
location /manager {
    proxy_pass http://127.0.0.1:9217;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

location /agent {
    proxy_pass http://127.0.0.1:9219;
    # 同上配置
}

location /merchant {
    proxy_pass http://127.0.0.1:9218;
    # 同上配置
}
```

📊 监控命令：
/www/wwwroot/ybdl.shop/monitor-bt.sh

🌐 访问地址：
- 门户: https://ybdl.shop
- 运营: https://ybdl.shop/manager
- 代理: https://ybdl.shop/agent
- 商户: https://ybdl.shop/merchant

⚠️ 注意事项：
1. 确保后端服务运行在对应端口
2. 检查防火墙端口开放状态
3. 域名需要正确解析到服务器IP
4. 定期备份网站文件和配置

🆘 故障排除：
- 网站无法访问 → 检查nginx状态和配置
- 反向代理失败 → 检查后端服务和端口
- SSL证书问题 → 重新申请或检查域名解析
- 宝塔面板无法访问 → bt restart 重启面板