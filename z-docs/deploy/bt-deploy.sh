#!/bin/bash

# UniPay Portal 宝塔面板部署脚本
# 适用于已安装宝塔面板的服务器
# 版本: v1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DOMAIN="ybdl.shop"
BT_WWWROOT="/www/wwwroot"
SITE_PATH="$BT_WWWROOT/$DOMAIN"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查宝塔面板
check_bt_panel() {
    log_info "检查宝塔面板..."
    
    if [ ! -f "/www/server/panel/BT-Panel" ]; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        echo "安装命令: curl -sSO http://download.bt.cn/install/install_panel.sh && bash install_panel.sh"
        exit 1
    fi
    
    if [ ! -d "/www/server/nginx" ]; then
        log_error "未检测到Nginx，请在宝塔面板中安装Nginx"
        exit 1
    fi
    
    log_success "宝塔面板检查通过"
}

# 检查站点是否存在
check_site_exists() {
    log_info "检查站点配置..."
    
    if [ ! -d "$SITE_PATH" ]; then
        log_warning "站点目录不存在: $SITE_PATH"
        log_info "请先在宝塔面板中创建站点: $DOMAIN"
        
        read -p "是否要创建站点目录? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            mkdir -p "$SITE_PATH"
            chown -R www:www "$SITE_PATH"
            log_success "站点目录已创建"
        else
            log_error "请先在宝塔面板中创建站点，然后重新运行此脚本"
            exit 1
        fi
    else
        log_success "站点目录已存在: $SITE_PATH"
    fi
}

# 备份现有文件
backup_files() {
    log_info "备份现有文件..."
    
    BACKUP_DIR="/tmp/unipay-backup-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    if [ -d "$SITE_PATH" ] && [ "$(ls -A $SITE_PATH)" ]; then
        cp -r "$SITE_PATH"/* "$BACKUP_DIR/" 2>/dev/null || true
        log_info "文件已备份到: $BACKUP_DIR"
    fi
}

# 部署文件
deploy_files() {
    log_info "部署项目文件..."
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 复制前端文件
    cp "$SCRIPT_DIR"/index.html "$SITE_PATH/" 2>/dev/null || true
    cp "$SCRIPT_DIR"/styles.css "$SITE_PATH/" 2>/dev/null || true
    cp "$SCRIPT_DIR"/script.js "$SITE_PATH/" 2>/dev/null || true
    cp "$SCRIPT_DIR"/404.html "$SITE_PATH/" 2>/dev/null || true
    cp "$SCRIPT_DIR"/50x.html "$SITE_PATH/" 2>/dev/null || true
    cp "$SCRIPT_DIR"/*.md "$SITE_PATH/" 2>/dev/null || true
    
    # 设置权限
    chown -R www:www "$SITE_PATH"
    chmod -R 755 "$SITE_PATH"
    
    log_success "项目文件部署完成"
}

# 配置防火墙端口
configure_firewall() {
    log_info "配置防火墙端口..."
    
    # 宝塔面板的防火墙配置
    BT_FIREWALL="/www/server/panel/data/port.pl"
    
    # 需要开放的端口
    PORTS=(9216 9217 9218 9219)
    
    for port in "${PORTS[@]}"; do
        # 检查端口是否已开放
        if ! grep -q "$port" "$BT_FIREWALL" 2>/dev/null; then
            # 使用宝塔命令行工具开放端口
            if command -v bt &> /dev/null; then
                bt firewall add_port "$port" "UniPay服务端口$port"
                log_info "已开放端口: $port"
            else
                log_warning "请手动在宝塔面板中开放端口: $port"
            fi
        else
            log_info "端口 $port 已开放"
        fi
    done
    
    log_success "防火墙配置完成"
}

# 显示nginx配置说明
show_nginx_config() {
    log_info "Nginx配置说明..."
    
    echo -e "${YELLOW}请按以下步骤配置Nginx:${NC}"
    echo ""
    echo "1. 登录宝塔面板"
    echo "2. 进入 '网站' 管理"
    echo "3. 找到站点 '$DOMAIN' 并点击 '设置'"
    echo "4. 选择 '配置文件' 选项卡"
    echo "5. 将以下配置内容复制到配置文件中："
    echo ""
    echo -e "${BLUE}配置文件位置: $(pwd)/bt-nginx.conf${NC}"
    echo ""
    echo "6. 点击 '保存' 并重载配置"
    echo ""
    
    # 显示配置文件内容的关键部分
    echo -e "${YELLOW}关键配置内容预览:${NC}"
    echo "----------------------------------------"
    grep -A 20 "# 运营平台代理" bt-nginx.conf 2>/dev/null || echo "请查看 bt-nginx.conf 文件"
    echo "----------------------------------------"
}

# 创建SSL配置说明
show_ssl_config() {
    log_info "SSL证书配置说明..."
    
    echo -e "${YELLOW}在宝塔面板中配置SSL证书:${NC}"
    echo ""
    echo "1. 在站点设置中选择 'SSL' 选项卡"
    echo "2. 选择 'Let's Encrypt' 免费证书"
    echo "3. 填写域名: $DOMAIN"
    echo "4. 点击 '申请' 获取证书"
    echo "5. 开启 '强制HTTPS'"
    echo ""
    echo "或者上传自己的证书文件"
}

# 创建监控脚本
create_monitor_script() {
    log_info "创建监控脚本..."
    
    cat > "$SITE_PATH/monitor-bt.sh" << 'EOF'
#!/bin/bash

# UniPay 宝塔面板服务监控脚本

DOMAIN="ybdl.shop"
SERVICES=(
    "9216:支付网关"
    "9217:运营平台"
    "9218:商户平台"
    "9219:代理商平台"
)

LOG_FILE="/www/wwwlogs/unipay-monitor.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

check_service() {
    local port=$1
    local name=$2
    
    if netstat -tuln 2>/dev/null | grep -q ":$port " || ss -tuln 2>/dev/null | grep -q ":$port "; then
        log_success "$name (端口 $port) - 运行正常"
        echo "$(date): $name (端口 $port) - 运行正常" >> $LOG_FILE
        return 0
    else
        log_error "$name (端口 $port) - 服务异常"
        echo "$(date): $name (端口 $port) - 服务异常" >> $LOG_FILE
        return 1
    fi
}

check_website() {
    local url="http://$DOMAIN"
    local https_url="https://$DOMAIN"
    
    log_info "检查网站访问..."
    
    if curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$url" | grep -q "200"; then
        log_success "HTTP访问正常"
    else
        log_error "HTTP访问异常"
    fi
    
    if curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$https_url" | grep -q "200"; then
        log_success "HTTPS访问正常"
    else
        log_error "HTTPS访问异常"
    fi
}

main() {
    echo "$(date): 开始服务检查" >> $LOG_FILE
    echo -e "${BLUE}UniPay 服务监控 - $(date)${NC}"
    echo "=================================="
    
    for service in "${SERVICES[@]}"; do
        port=$(echo $service | cut -d: -f1)
        name=$(echo $service | cut -d: -f2)
        check_service $port "$name"
    done
    
    echo ""
    check_website
    
    echo "$(date): 服务检查完成" >> $LOG_FILE
    echo "---" >> $LOG_FILE
}

main "$@"
EOF

    chmod +x "$SITE_PATH/monitor-bt.sh"
    
    log_success "监控脚本已创建: $SITE_PATH/monitor-bt.sh"
}

# 显示部署结果
show_result() {
    echo ""
    log_success "=== 宝塔面板部署完成 ==="
    echo ""
    log_info "网站目录: $SITE_PATH"
    log_info "监控脚本: $SITE_PATH/monitor-bt.sh"
    log_info "Nginx配置: $(pwd)/bt-nginx.conf"
    echo ""
    log_info "访问地址:"
    log_info "  门户首页: http://$DOMAIN"
    log_info "  运营平台: http://$DOMAIN/manager"
    log_info "  代理商平台: http://$DOMAIN/agent"
    log_info "  商户平台: http://$DOMAIN/merchant"
    echo ""
    log_warning "后续步骤:"
    echo "1. 在宝塔面板中配置Nginx (参考上面的说明)"
    echo "2. 配置SSL证书 (可选)"
    echo "3. 确保后端服务正在运行"
    echo "4. 运行监控脚本: $SITE_PATH/monitor-bt.sh"
    echo ""
    log_info "宝塔面板访问: http://服务器IP:8888"
}

# 主函数
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "      UniPay Portal 宝塔面板部署脚本 v1.0"
    echo "=================================================="
    echo -e "${NC}"
    
    check_bt_panel
    check_site_exists
    backup_files
    deploy_files
    configure_firewall
    create_monitor_script
    show_nginx_config
    show_ssl_config
    show_result
    
    log_success "部署脚本执行完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi