# UniPay Nginx 部署指南

## 概述

本指南提供了UniPay统一支付平台的Nginx部署方案，支持两种部署模式：

1. **多域名模式** - 每个服务使用独立域名
2. **单域名模式** - 所有服务通过路径区分

## 系统架构

| 服务名称 | 端口 | 功能描述 |
|---------|------|----------|
| sys-manager | 8080 | 管理后台 |
| sys-payment | 9216 | 支付网关 |
| sys-merchant | 9218 | 商户平台 |
| sys-agent | 9219 | 代理商平台 |
| game-server | 8088 | 游戏服务 |

## 快速部署

### 1. 准备工作

```bash
# 确保所有Java服务正在运行
sudo netstat -tlnp | grep -E ':(8080|8088|9216|9218|9219)'

# 安装nginx (如果未安装)
# Ubuntu/Debian
sudo apt-get update && sudo apt-get install nginx

# CentOS/RHEL
sudo yum install nginx
```

### 2. 使用部署脚本

```bash
# 赋予执行权限
chmod +x deploy-nginx.sh

# 部署单域名模式 (推荐用于开发/测试)
sudo ./deploy-nginx.sh single

# 部署多域名模式 (推荐用于生产环境)
sudo ./deploy-nginx.sh multi
```

### 3. 手动部署

#### 单域名模式

```bash
# 复制配置文件
sudo cp nginx-unipay-single.conf /etc/nginx/sites-available/unipay

# 创建软链接
sudo ln -s /etc/nginx/sites-available/unipay /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载nginx
sudo systemctl reload nginx
```

#### 多域名模式

```bash
# 复制配置文件
sudo cp nginx-unipay.conf /etc/nginx/sites-available/unipay

# 创建软链接
sudo ln -s /etc/nginx/sites-available/unipay /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载nginx
sudo systemctl reload nginx
```

## 访问地址

### 单域名模式
- 管理后台: `http://your-domain/manager/`
- 商户平台: `http://your-domain/merchant/`
- 代理商平台: `http://your-domain/agent/`
- 支付网关: `http://your-domain/pay/`
- 游戏服务: `http://your-domain/game/`

### 多域名模式
- 管理后台: `http://manager.unipay.com/`
- 商户平台: `http://merchant.unipay.com/`
- 代理商平台: `http://agent.unipay.com/`
- 支付网关: `http://pay.unipay.com/`
- 游戏服务: `http://game.unipay.com/`

## DNS配置

### 多域名模式DNS设置

```
manager.unipay.com    A    your-server-ip
merchant.unipay.com   A    your-server-ip
agent.unipay.com      A    your-server-ip
pay.unipay.com        A    your-server-ip
game.unipay.com       A    your-server-ip
```

### 本地测试 (修改hosts文件)

```bash
# Linux/Mac: /etc/hosts
# Windows: C:\Windows\System32\drivers\etc\hosts

127.0.0.1 manager.unipay.com
127.0.0.1 merchant.unipay.com
127.0.0.1 agent.unipay.com
127.0.0.1 pay.unipay.com
127.0.0.1 game.unipay.com
```

## 安全配置

### 1. HTTPS配置 (生产环境必须)

```bash
# 获取SSL证书 (Let's Encrypt)
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d manager.unipay.com -d merchant.unipay.com -d agent.unipay.com -d pay.unipay.com -d game.unipay.com
```

### 2. 防火墙配置

```bash
# UFW (Ubuntu)
sudo ufw allow 'Nginx Full'
sudo ufw allow ssh
sudo ufw enable

# iptables
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

### 3. 生产环境安全建议

1. **关闭Swagger文档**
   ```yaml
   knife4j:
     enable: false
   ```

2. **启用访问限制**
   ```nginx
   # 限制管理后台访问IP
   location /manager/ {
       allow ***********/24;
       deny all;
       # ... 其他配置
   }
   ```

3. **启用基本认证**
   ```bash
   # 创建密码文件
   sudo htpasswd -c /etc/nginx/.htpasswd admin
   
   # 在nginx配置中添加
   auth_basic "Restricted Access";
   auth_basic_user_file /etc/nginx/.htpasswd;
   ```

## 监控和日志

### 日志位置
- 访问日志: `/var/log/nginx/unipay-*-access.log`
- 错误日志: `/var/log/nginx/unipay-*-error.log`

### 日志分析
```bash
# 查看访问统计
sudo tail -f /var/log/nginx/unipay-manager-access.log

# 查看错误日志
sudo tail -f /var/log/nginx/unipay-manager-error.log

# 分析访问量
sudo awk '{print $1}' /var/log/nginx/unipay-*-access.log | sort | uniq -c | sort -nr | head -10
```

## 故障排除

### 1. 检查服务状态
```bash
# 检查nginx状态
sudo systemctl status nginx

# 检查Java服务
sudo netstat -tlnp | grep java

# 检查端口占用
sudo lsof -i :80
```

### 2. 常见问题

**问题**: 502 Bad Gateway
**解决**: 检查后端Java服务是否正常运行

**问题**: 404 Not Found
**解决**: 检查nginx配置中的proxy_pass地址

**问题**: 静态资源加载失败
**解决**: 检查静态资源路径配置

### 3. 配置测试
```bash
# 测试nginx配置
sudo nginx -t

# 重载配置
sudo nginx -s reload

# 查看nginx进程
ps aux | grep nginx
```

## 性能优化

### 1. 启用Gzip压缩
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

### 2. 调整缓冲区大小
```nginx
proxy_buffering on;
proxy_buffer_size 4k;
proxy_buffers 8 4k;
proxy_busy_buffers_size 8k;
```

### 3. 连接池优化
```nginx
upstream unipay_manager {
    server 127.0.0.1:8080 max_fails=3 fail_timeout=30s;
    keepalive 32;
}
```

## 维护命令

```bash
# 查看nginx版本
nginx -v

# 测试配置文件
sudo nginx -t

# 重载配置
sudo systemctl reload nginx

# 重启nginx
sudo systemctl restart nginx

# 查看nginx状态
sudo systemctl status nginx

# 查看错误日志
sudo journalctl -u nginx -f