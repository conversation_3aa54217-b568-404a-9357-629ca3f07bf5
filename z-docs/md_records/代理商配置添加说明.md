# 代理商配置添加说明

## 问题描述

在管理平台的系统管理 -> 系统配置中，原本只有4个配置项：
1. mgrSiteUrl - 运营平台网址
2. mchSiteUrl - 商户平台网址  
3. paySiteUrl - 支付网关地址
4. ossPublicSiteUrl - 公共OSS访问地址

缺少代理商平台的配置项，需要添加代理商相关的配置。

## 解决方案

### 1. 数据库层面修改

#### 1.1 初始化脚本更新
**文件**: `z-docs/sql/init.sql`
- 在第711行添加了代理商平台网址配置：
```sql
INSERT INTO `t_sys_config` VALUES ('agentSiteUrl', '代理商平台网址(不包含结尾/)', '代理商平台网址(不包含结尾/)', 'applicationConfig', '系统应用配置', 'http://127.0.0.1:9219', 'text', 0, '2021-5-18 14:46:10');
```

#### 1.2 增量更新脚本
**文件**: `z-docs/sql/add_agent_config.sql`
- 创建了专门的增量更新脚本，用于给现有系统添加代理商配置
- 使用了安全的插入语句，避免重复插入

### 2. 后端代码修改

#### 2.1 配置模型类更新
**文件**: `core/src/main/java/com/unipay/core/model/DBApplicationConfig.java`
- 添加了 `agentSiteUrl` 字段
- 新增了3个代理商相关的方法：
  - `genAgentLoginSuccessUrl()` - 生成代理商登录成功跳转地址
  - `genAgentPasswordResetUrl(String token)` - 生成代理商密码重置链接
  - `genAgentProfitNotifyUrl(String agentNo)` - 生成代理商分润通知链接

#### 2.2 系统配置控制器更新
**文件**: `sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java`
- 在API文档注解中添加了 `agentSiteUrl` 参数说明
- 调整了参数顺序，使其更符合逻辑

### 3. 配置项用途说明

#### 3.1 现有配置项用途
1. **mgrSiteUrl**: 运营平台访问地址，用于生成运营平台相关链接
2. **mchSiteUrl**: 商户平台访问地址，用于商户相关功能链接生成
3. **paySiteUrl**: 支付网关地址，用于生成收银台、支付回调等链接
4. **ossPublicSiteUrl**: 文件访问地址，用于头像、资质文件等静态资源访问

#### 3.2 新增代理商配置用途
**agentSiteUrl**: 代理商平台访问地址，用于：
- 代理商登录成功后的跳转
- 代理商密码重置链接
- 代理商分润通知链接
- 代理商相关邮件通知中的链接

### 4. 部署说明

#### 4.1 新系统部署
- 直接使用更新后的 `init.sql` 脚本即可

#### 4.2 现有系统升级
- 执行 `z-docs/sql/add_agent_config.sql` 脚本
- 重启相关服务以刷新配置缓存

#### 4.3 配置值调整
根据实际部署环境调整配置值：

**开发环境**:
```
agentSiteUrl: http://127.0.0.1:9219
```

**生产环境**:
```
agentSiteUrl: https://agent.yourdomain.com
```

### 5. 验证方法

1. **数据库验证**:
```sql
SELECT * FROM t_sys_config WHERE config_key = 'agentSiteUrl';
```

2. **API验证**:
```
GET /api/sysConfigs/applicationConfig
```
返回结果中应包含 `agentSiteUrl` 配置项

3. **前端验证**:
- 登录管理平台
- 进入 系统管理 -> 系统配置 -> 应用配置
- 应该能看到"代理商平台网址"配置项

### 6. 相关文件清单

#### 修改的文件
- `z-docs/sql/init.sql` - 数据库初始化脚本
- `core/src/main/java/com/unipay/core/model/DBApplicationConfig.java` - 配置模型类
- `sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java` - 系统配置控制器

#### 新增的文件
- `z-docs/sql/add_agent_config.sql` - 代理商配置增量更新脚本
- `z-docs/系统配置说明.md` - 系统配置详细说明文档
- `z-docs/代理商配置添加说明.md` - 本说明文档

### 7. 注意事项

1. **配置缓存**: 配置更新后会通过MQ异步通知各服务刷新缓存
2. **URL格式**: 所有URL配置都不应包含结尾的斜杠 `/`
3. **HTTPS**: 生产环境建议使用HTTPS协议
4. **端口一致性**: 确保配置的端口与实际服务监听端口一致

## 总结

通过以上修改，成功为UniPay系统添加了代理商平台网址配置，使系统配置更加完整。现在系统支持5个平台的网址配置，能够满足多平台架构的需求。
