import moment from 'moment'
import 'moment/locale/zh-cn'
moment.locale('zh-cn')

// Vue 3 全局属性方式定义过滤器
export const filters = {
  NumberFormat: function (value) {
    if (!value) {
      return '0'
    }
    const intPartFormat = value.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') // 将整数部分逢三一断
    return intPartFormat
  },

  dayjs: function (dataStr, pattern = 'YYYY-MM-DD HH:mm:ss') {
    return moment(dataStr).format(pattern)
  },

  moment: function (dataStr, pattern = 'YYYY-MM-DD HH:mm:ss') {
    return moment(dataStr).format(pattern)
  },

  dateFormat: function (dataStr, pattern = 'YYYY-MM-DD HH:mm:ss') {
    if (!dataStr) return ''
    return moment(dataStr).format(pattern)
  }
}
