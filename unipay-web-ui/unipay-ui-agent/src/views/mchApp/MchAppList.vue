<template>
  <page-header-wrapper>
    <a-card>
      <div class="table-page-search-wrapper">
        <a-form layout="inline" class="table-head-ground">
          <div class="table-layer">
            <jeepay-text-up v-model:value="searchData.appId" :placeholder="'应用AppId'" />
            <jeepay-text-up v-model:value="searchData.appName" :placeholder="'应用名称'" />
            <jeepay-text-up v-model:value="searchData.mchNo" :placeholder="'商户号'" />
            <a-select
              v-model:value="searchData.state"
              placeholder="应用状态"
              class="table-head-layout"
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="0">禁用</a-select-option>
              <a-select-option value="1">启用</a-select-option>
            </a-select>
            <span class="table-page-search-submitButtons" style="flex-grow: 0; flex-shrink: 0">
              <a-button type="primary" :loading="vdata.btnLoading" @click="queryFunc">
                查询
              </a-button>
              <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
            </span>
          </div>
        </a-form>
      </div>

      <!-- 列表渲染 -->
      <JeepayTable
        ref="infoTable"
        :init-data="true"
        :req-table-data-func="reqTableDataFunc"
        :table-columns="tableColumns"
        :search-data="searchData"
        row-key="appId"
        @btnLoadClose="vdata.btnLoading = false"
      >
        <template #opRow>
          <a-button type="primary" @click="addFunc" v-if="$access('ENT_MCH_APP_ADD')">
            新建
          </a-button>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'appId'">
            <b>{{ record.appId }}</b>
          </template>
          
          <template v-if="column.key === 'mchNo'">
            <a-tag color="blue">{{ record.mchNo }}</a-tag>
          </template>

          <!-- 自定义插槽 -->
          <template v-if="column.key === 'state'">
            <a-badge
              :status="record.state === 0 ? 'error' : 'processing'"
              :text="record.state === 0 ? '禁用' : '启用'"
            />
          </template>

          <template v-if="column.key === 'operation'">
            <a-button
              type="link"
              @click="editFunc(record.appId)"
              v-if="$access('ENT_MCH_APP_EDIT')"
            >
              修改
            </a-button>
            <a-button
              type="link"
              @click="viewFunc(record.appId)"
              v-if="$access('ENT_MCH_APP_VIEW')"
            >
              详情
            </a-button>
            <a-button
              type="link"
              style="color: red"
              @click="delFunc(record.appId)"
              v-if="$access('ENT_MCH_APP_DEL')"
            >
              删除
            </a-button>
          </template>
        </template>
      </JeepayTable>
    </a-card>
    <!-- 新增页面组件  -->
    <InfoAddOrEdit ref="infoAddOrEdit" :callback-func="searchFunc" />
    <!-- 详情页面组件  -->
    <InfoDetail ref="infoDetail" :callback-func="searchFunc" />
  </page-header-wrapper>
</template>

<script setup lang="ts">
import { API_URL_MCH_APP, req } from '@/api/manage'
import InfoAddOrEdit from './AddOrEdit.vue'
import InfoDetail from './Detail.vue'
import { ref, reactive, getCurrentInstance } from 'vue'

const { $infoBox, $access } = getCurrentInstance()!.appContext.config.globalProperties

const infoDetail = ref()
const infoAddOrEdit = ref()
const infoTable = ref()

// eslint-disable-next-line no-unused-vars
let tableColumns = reactive([
  {
    key: 'appId',
    fixed: 'left',
    width: '280px',
    title: '应用AppId',
  },
  { key: 'appName', title: '应用名称', dataIndex: 'appName' },
  { key: 'mchNo', title: '商户号', dataIndex: 'mchNo', width: '150px' },
  { key: 'state', title: '状态', width: '100px' },
  { key: 'createdAt', dataIndex: 'createdAt', title: '创建日期' },
  { key: 'operation', title: '操作', width: '200px', fixed: 'right', align: 'center' },
])

const vdata: any = reactive({
  btnLoading: false,
})

let searchData = reactive({
  appId: '',
  appName: '',
  mchNo: '',
  state: ''
})

function queryFunc() {
  vdata.btnLoading = true
  infoTable.value.refTable(true)
}

function resetSearch() {
  searchData.appId = ''
  searchData.appName = ''
  searchData.mchNo = ''
  searchData.state = ''
}

// 请求table接口数据
function reqTableDataFunc(params: any) {
  return req.list(API_URL_MCH_APP, params)
}

function searchFunc() {
  // 点击【查询】按钮点击事件
  infoTable.value.refTable(true)
}

function addFunc() {
  // 业务通用【新增】 函数
  infoAddOrEdit.value.show()
}

function editFunc(recordId: string) {
  // 业务通用【修改】 函数
  infoAddOrEdit.value.show(recordId)
}

function viewFunc(recordId: string) {
  // 业务通用【详情】 函数
  infoDetail.value.show(recordId)
}

function delFunc(appId: string) {
  $infoBox.confirmDanger('确认删除？', '', () => {
    req.delById(API_URL_MCH_APP, appId).then(() => {
      $infoBox.message.success('删除成功！')
      searchFunc()
    })
  })
}
</script>
