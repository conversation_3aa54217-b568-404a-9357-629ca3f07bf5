<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <a-drawer
    v-model:open="vdata.open"
    :title="'应用详情'"
    :body-style="{ paddingBottom: '80px' }"
    width="40%"
    @close="onClose"
  >
    <a-row justify="space-between" type="flex">
      <a-col :sm="12">
        <a-descriptions>
          <a-descriptions-item label="应用AppId">
            <a-typography-text copyable>{{ vdata.detailData.appId }}</a-typography-text>
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :sm="12">
        <a-descriptions>
          <a-descriptions-item label="应用名称">
            {{ vdata.detailData.appName }}
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :sm="12">
        <a-descriptions>
          <a-descriptions-item label="商户号">
            <a-tag color="blue">{{ vdata.detailData.mchNo }}</a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :sm="12">
        <a-descriptions>
          <a-descriptions-item label="状态">
            <a-tag :color="vdata.detailData.state === 1 ? 'green' : 'volcano'">
              {{ vdata.detailData.state === 0 ? '禁用' : '启用' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :sm="24">
        <a-descriptions>
          <a-descriptions-item label="应用私钥">
            <a-typography-text copyable>{{ vdata.detailData.appSecret }}</a-typography-text>
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :sm="12">
        <a-descriptions>
          <a-descriptions-item label="创建者">
            {{ vdata.detailData.createdBy }}
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :sm="12">
        <a-descriptions>
          <a-descriptions-item label="创建时间">
            {{ vdata.detailData.createdAt }}
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :sm="12">
        <a-descriptions>
          <a-descriptions-item label="更新时间">
            {{ vdata.detailData.updatedAt }}
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :sm="24" v-if="vdata.detailData.remark">
        <a-descriptions>
          <a-descriptions-item label="备注">
            {{ vdata.detailData.remark }}
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
    </a-row>

    <div class="drawer-btn-center">
      <a-button @click="onClose">关闭</a-button>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { API_URL_MCH_APP, req } from '@/api/manage'
import { reactive, getCurrentInstance } from 'vue'

const { $infoBox } = getCurrentInstance()!.appContext.config.globalProperties

const props: any = defineProps({
  callbackFunc: { type: Function },
})

const vdata: any = reactive({
  open: false, // 是否显示弹层/抽屉
  detailData: {}, // 详情数据
})

function show(recordId) {
  // 弹层打开事件
  vdata.detailData = {}
  
  req.getById(API_URL_MCH_APP, recordId).then((res) => {
    vdata.detailData = res
    vdata.open = true
  }).catch(() => {
    $infoBox.message.error('获取应用详情失败')
  })
}

function onClose() {
  vdata.open = false
}

defineExpose({ show })
</script>

<style scoped>
.drawer-btn-center {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
  z-index: 1;
}
</style>
