<template>
  <a-drawer
    v-model:open="vdata.open"
    :mask-closable="false"
    :title="vdata.isAdd ? '新增应用' : '修改应用'"
    :body-style="{ paddingBottom: '80px' }"
    width="40%"
    class="drawer-width"
    @close="onClose"
  >
    <a-form
      ref="infoFormModel"
      :model="vdata.saveObject"
      layout="vertical"
      :rules="rules"
      name="basic"
    >
      <a-row justify="space-between" type="flex">
        <a-col :span="10" v-if="!vdata.isAdd">
          <a-form-item label="应用AppId" name="appId">
            <a-input
              v-model:value="vdata.saveObject.appId"
              placeholder="系统自动生成"
              :disabled="true"
            />
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="商户号" name="mchNo">
            <a-select
              v-model:value="vdata.saveObject.mchNo"
              placeholder="请选择商户"
              :disabled="!vdata.isAdd"
              show-search
              :filter-option="filterOption"
            >
              <a-select-option
                v-for="mch in vdata.mchList"
                :key="mch.mchNo"
                :value="mch.mchNo"
              >
                {{ mch.mchNo }} - {{ mch.mchName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row justify="space-between" type="flex">
        <a-col :span="10">
          <a-form-item label="应用名称" name="appName">
            <a-input v-model:value="vdata.saveObject.appName" placeholder="请输入应用名称" />
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="状态" name="state">
            <a-radio-group v-model:value="vdata.saveObject.state">
              <a-radio :value="1">启用</a-radio>
              <a-radio :value="0">禁用</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row justify="space-between" type="flex">
        <a-col :span="22">
          <a-form-item label="应用私钥" name="appSecret">
            <a-input-group compact>
              <a-input
                v-model:value="vdata.saveObject.appSecret"
                :placeholder="vdata.saveObject.appSecret_ph"
                style="width: calc(100% - 100px)"
              />
              <a-button type="primary" @click="randomAppSecret" style="width: 100px">
                随机生成
              </a-button>
            </a-input-group>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row justify="space-between" type="flex">
        <a-col :span="22">
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="vdata.saveObject.remark"
              placeholder="请输入备注信息"
              :rows="3"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <div class="drawer-btn-center">
      <a-button @click="onClose" style="margin-right: 8px">取消</a-button>
      <a-button type="primary" @click="onSubmit" :loading="vdata.saveLoading">提交</a-button>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { API_URL_MCH_APP, API_URL_MCH_LIST, req } from '@/api/manage'
import { reactive, ref, getCurrentInstance } from 'vue'
import { generateRandomString } from '@/utils/util'

const { $infoBox } = getCurrentInstance()!.appContext.config.globalProperties

const props: any = defineProps({
  callbackFunc: { type: Function },
})

const infoFormModel = ref()

const vdata: any = reactive({
  open: false, // 是否显示弹层/抽屉
  isAdd: true, // 是否新增
  saveLoading: false, // 保存按钮loading
  saveObject: {}, // 保存对象
  mchList: [], // 商户列表
})

const rules = reactive({
  mchNo: [{ required: true, message: '请选择商户', trigger: 'change' }],
  appName: [{ required: true, message: '请输入应用名称', trigger: 'blur' }],
  appSecret: [{ required: true, message: '请输入应用私钥或点击随机生成', trigger: 'blur' }],
  state: [{ required: true, message: '请选择状态', trigger: 'change' }],
})

function show(appId) {
  // 弹层打开事件
  vdata.isAdd = !appId
  vdata.saveObject = {
    state: 1,
    appSecret: '',
    appSecret_ph: '请输入应用私钥',
  }

  if (infoFormModel.value) {
    infoFormModel.value.resetFields()
  }

  // 加载商户列表
  loadMchList()

  if (!vdata.isAdd) {
    // 修改信息 延迟展示弹层
    vdata.appId = appId
    // 拉取详情
    req.getById(API_URL_MCH_APP, appId).then((res) => {
      vdata.saveObject = res
      vdata.saveObject.appSecret_ph = res.appSecret
      vdata.saveObject.appSecret = ''
      vdata.open = true
    })
  } else {
    vdata.open = true // 展示弹层信息
  }
}

// 加载商户列表
function loadMchList() {
  req.list(API_URL_MCH_LIST, { pageSize: -1 }).then((res) => {
    vdata.mchList = res.records || []
  })
}

// 商户筛选
function filterOption(input, option) {
  return (
    option.children[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0
  )
}

// 随机生成应用私钥
function randomAppSecret() {
  vdata.saveObject.appSecret = generateRandomString(128)
}

function onSubmit() {
  infoFormModel.value
    .validate()
    .then(() => {
      vdata.saveLoading = true
      const reqParams = JSON.parse(JSON.stringify(vdata.saveObject))

      let reqPromise
      if (vdata.isAdd) {
        reqPromise = req.add(API_URL_MCH_APP, reqParams)
      } else {
        reqPromise = req.updateById(API_URL_MCH_APP, vdata.appId, reqParams)
      }

      reqPromise
        .then(() => {
          $infoBox.message.success('操作成功！')
          vdata.saveLoading = false
          vdata.open = false
          props.callbackFunc()
        })
        .catch(() => {
          vdata.saveLoading = false
        })
    })
    .catch(() => {
      // 表单验证失败
    })
}

function onClose() {
  vdata.open = false
}

defineExpose({ show })
</script>

<style scoped>
.drawer-btn-center {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
  z-index: 1;
}
</style>
