<template>
  <page-header-wrapper>
    <a-card>
      <div class="table-page-search-wrapper">
        <a-form layout="inline" class="table-head-ground">
          <div class="table-layer">
            <jeepay-text-up v-model:value="searchData.agentNo" :placeholder="'代理商号'" />
            <jeepay-text-up v-model:value="searchData.agentName" :placeholder="'代理商名称'" />
            <jeepay-text-up v-model:value="searchData.contactName" :placeholder="'联系人'" />
            <a-select
              v-model:value="searchData.state"
              placeholder="状态"
              class="table-head-layout"
              allow-clear
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option :value="1">正常</a-select-option>
              <a-select-option :value="0">停用</a-select-option>
            </a-select>
            <jeepay-text-up v-model:value="searchData.contactTel" :placeholder="'联系电话'" />
            <a-select
              v-model:value="searchData.agentLevel"
              placeholder="代理商层级"
              class="table-head-layout"
              allow-clear
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option :value="1">1级代理商</a-select-option>
              <a-select-option :value="2">2级代理商</a-select-option>
              <a-select-option :value="3">3级代理商</a-select-option>
            </a-select>
            <span class="table-page-search-submitButtons" style="flex-grow: 0; flex-shrink: 0">
              <a-button type="primary" @click="queryFunc" :loading="btnLoading">查询</a-button>
              <a-button style="margin-left: 8px" @click="resetSearchFunc">重置</a-button>
            </span>
          </div>
        </a-form>
      </div>

      <!-- 列表渲染 -->
      <JeepayTable
        ref="infoTable"
        :init-data="true"
        :req-table-data-func="reqTableDataFunc"
        :table-columns="tableColumns"
        :search-data="searchData"
        row-key="agentNo"
        @btnLoadClose="btnLoading = false"
      >
        <template #opRow>
          <a-button type="primary" @click="addFunc" v-if="$access('ENT_AGENT_INFO_ADD')">
            新建
          </a-button>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'agentName'">
            <a @click="detailFunc(record.agentNo)" v-if="$access('ENT_AGENT_INFO_VIEW')">
              {{ record.agentName }}
            </a>
            <span v-else>{{ record.agentName }}</span>
          </template>

          <template v-if="column.key === 'agentLevel'">
            <a-tag :color="getAgentLevelColor(record.agentLevel)">
              {{ getAgentLevelText(record.agentLevel) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'profitRate'">
            {{ (record.profitRate * 100).toFixed(2) }}%
          </template>

          <template v-if="column.key === 'state'">
            <a-tag :color="record.state === 1 ? 'green' : 'red'">
              {{ record.state === 1 ? '正常' : '停用' }}
            </a-tag>
          </template>

          <template v-if="column.key === 'createdAt'">
            {{ $filters.dateFormat(record.createdAt) }}
          </template>

          <template v-if="column.key === 'operation'">
            <a-button
              type="link"
              @click="editFunc(record.agentNo)"
              v-if="$access('ENT_AGENT_INFO_EDIT')"
            >
              修改
            </a-button>

            <a-button
              type="link"
              style="color: red"
              @click="delFunc(record.agentNo)"
              v-if="$access('ENT_AGENT_INFO_DELETE')"
            >
              删除
            </a-button>
          </template>
        </template>
      </JeepayTable>
    </a-card>
    <!-- 新增页面组件  -->
    <InfoAddOrEdit ref="infoAddOrEdit" :callback-func="searchFunc" />
    <!-- 详情页面组件  -->
    <InfoDetail ref="infoDetail" :callback-func="searchFunc" />
  </page-header-wrapper>
</template>

<script setup lang="ts">
import { API_URL_AGENT_LIST, req, reqLoad } from '@/api/manage'
import InfoAddOrEdit from './AddOrEdit.vue'
import InfoDetail from './Detail.vue'
import { ref, reactive, getCurrentInstance } from 'vue'

const { $infoBox, $access, $filters } = getCurrentInstance()!.appContext.config.globalProperties

const infoTable = ref()
const infoAddOrEdit = ref()
const infoDetail = ref()

// eslint-disable-next-line no-unused-vars
let tableColumns = reactive([
  { key: 'agentName', dataIndex: 'agentName', fixed: 'left', width: '200px', title: '代理商名称' },
  { key: 'agentNo', title: '代理商号', dataIndex: 'agentNo' },
  { key: 'parentAgentNo', title: '上级代理商号', dataIndex: 'parentAgentNo' },
  { key: 'agentLevel', title: '代理商层级', dataIndex: 'agentLevel', width: '120px' },
  { key: 'contactName', title: '联系人', dataIndex: 'contactName' },
  { key: 'contactTel', title: '联系电话', dataIndex: 'contactTel' },
  { key: 'profitRate', title: '分润比例', dataIndex: 'profitRate', width: '100px' },
  { key: 'state', title: '状态', dataIndex: 'state', width: '100px' },
  { key: 'createdAt', dataIndex: 'createdAt', title: '创建日期', width: '150px' },
  { key: 'operation', title: '操作', width: '120px', fixed: 'right', align: 'center' },
])

const btnLoading = ref(false)
let searchData: any = ref({})

function queryFunc() {
  btnLoading.value = true
  infoTable.value.refTable(true)
}

function resetSearchFunc() {
  searchData.value = {}
  queryFunc()
}

// 请求table接口数据
function reqTableDataFunc(params) {
  return req.list(API_URL_AGENT_LIST, params)
}

function searchFunc() {
  // 点击【查询】按钮点击事件
  infoTable.value.refTable(true)
}

function addFunc() {
  // 业务通用【新增】 函数
  infoAddOrEdit.value.show()
}

function editFunc(recordId) {
  // 业务通用【修改】 函数
  infoAddOrEdit.value.show(recordId)
}

function detailFunc(recordId) {
  // 代理商详情页
  infoDetail.value.show(recordId)
}

// 删除代理商
function delFunc(recordId) {
  $infoBox.confirmDanger('确认删除？', '该操作将删除代理商下所有配置及用户信息', () => {
    reqLoad.delById(API_URL_AGENT_LIST, recordId).then((res) => {
      infoTable.value.refTable(true)
      $infoBox.message.success('删除成功')
    })
  })
}

// 获取代理商层级颜色
function getAgentLevelColor(level) {
  const colors = {
    1: 'blue',
    2: 'green',
    3: 'orange',
    4: 'purple',
    5: 'cyan'
  }
  return colors[level] || 'default'
}

// 获取代理商层级文本
function getAgentLevelText(level) {
  const texts = {
    1: '一级代理商',
    2: '二级代理商',
    3: '三级代理商',
    4: '四级代理商',
    5: '五级代理商'
  }
  return texts[level] || `${level}级代理商`
}
</script>
