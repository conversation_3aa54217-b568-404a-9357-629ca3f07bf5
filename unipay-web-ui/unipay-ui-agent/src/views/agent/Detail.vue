<template>
  <a-drawer
    title="代理商详情"
    :width="800"
    :open="vdata.open"
    :body-style="{ paddingBottom: '80px' }"
    @close="onClose"
  >
    <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-row>
        <a-col :span="12">
          <a-form-item label="代理商号">
            <a-input :value="vdata.detailData.agentNo" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="代理商名称">
            <a-input :value="vdata.detailData.agentName" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="代理商简称">
            <a-input :value="vdata.detailData.agentShortName" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="登录用户名">
            <a-input :value="vdata.detailData.loginUsername || '未设置'" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="代理商层级">
            <a-tag :color="getAgentLevelColor(vdata.detailData.agentLevel)">
              {{ getAgentLevelText(vdata.detailData.agentLevel) }}
            </a-tag>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="上级代理商">
            <a-input :value="vdata.parentAgentName || vdata.detailData.parentAgentNo || '无'" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="代理商状态">
            <a-tag :color="vdata.detailData.state === 1 ? 'green' : 'red'">
              {{ vdata.detailData.state === 1 ? '正常' : '停用' }}
            </a-tag>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系人姓名">
            <a-input :value="vdata.detailData.contactName" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系人手机">
            <a-input :value="vdata.detailData.contactTel" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系人邮箱">
            <a-input :value="vdata.detailData.contactEmail" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="分润比例">
            <a-input :value="vdata.detailData.profitRate ? (vdata.detailData.profitRate * 100).toFixed(2) + '%' : '0%'" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="省份">
            <a-input :value="vdata.detailData.province" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="城市">
            <a-input :value="vdata.detailData.city" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="区县">
            <a-input :value="vdata.detailData.district" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="详细地址">
            <a-input :value="vdata.detailData.address" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="创建时间">
            <a-input :value="$filters.dateFormat(vdata.detailData.createdAt)" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="更新时间">
            <a-input :value="$filters.dateFormat(vdata.detailData.updatedAt)" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注信息">
            <a-textarea
              :value="vdata.detailData.remark"
              :auto-size="{ minRows: 2, maxRows: 6 }"
              :disabled="true"
              placeholder="暂无备注"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="drawer-btn-center">
      <a-button :style="{ marginRight: '8px' }" style="margin-right: 8px" @click="onClose">
        关闭
      </a-button>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { API_URL_AGENT_LIST, req } from '@/api/manage'
import { reactive, getCurrentInstance } from 'vue'

const { $infoBox, $filters } = getCurrentInstance()!.appContext.config.globalProperties

const props: any = defineProps({
  callbackFunc: { type: Function },
})

const vdata: any = reactive({
  open: false, // 是否显示弹层/抽屉
  detailData: {}, // 详情数据
  parentAgentName: '', // 上级代理商名称
})

function show(recordId) {
  // 弹层打开事件
  vdata.detailData = {}
  vdata.parentAgentName = ''
  
  req.getById(API_URL_AGENT_LIST, recordId).then((res) => {
    vdata.detailData = res
    
    // 如果有上级代理商，获取上级代理商名称
    if (res.parentAgentNo) {
      req.getById(API_URL_AGENT_LIST, res.parentAgentNo).then((parentRes) => {
        vdata.parentAgentName = parentRes.agentName
      }).catch(() => {
        vdata.parentAgentName = '未知'
      })
    }
    
    vdata.open = true
  }).catch((error) => {
    $infoBox.message.error('获取代理商详情失败')
  })
}

function onClose() {
  vdata.open = false
}

// 获取代理商层级颜色
function getAgentLevelColor(level) {
  const colors = {
    1: 'blue',
    2: 'green',
    3: 'orange',
    4: 'purple',
    5: 'cyan'
  }
  return colors[level] || 'default'
}

// 获取代理商层级文本
function getAgentLevelText(level) {
  const texts = {
    1: '一级代理商',
    2: '二级代理商',
    3: '三级代理商',
    4: '四级代理商',
    5: '五级代理商'
  }
  return texts[level] || `${level}级代理商`
}

defineExpose({
  show
})
</script>

<style scoped>
.drawer-btn-center {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
  z-index: 1;
}
</style>
