<template>
  <div id="userLayout" :class="['user-layout-wrapper']">
    <div class="container">
      <div class="user-layout-lang" />
      <div class="user-layout-content">
        <div class="top">
          <div class="header">
            <a href="/">
              <img src="@/assets/logo.svg" class="logo" alt="logo" />
            </a>
          </div>
          <div class="desc">
            <img src="@/assets/svg/operate.svg" class="logo" alt="logo" />
            <span>代理商平台注册</span>
          </div>
        </div>

        <div class="main">
          <a-form
            ref="formRegister"
            class="user-layout-register"
            :model="formState"
            :rules="rules"
            layout="vertical"
            @finish="handleSubmit"
          >
            <!-- 错误提示信息 -->
            <a-alert
              v-if="showRegisterErrorInfo"
              type="error"
              show-icon
              style="margin-bottom: 24px"
              :message="showRegisterErrorInfo"
            />

            <!-- 成功提示信息 -->
            <a-alert
              v-if="showRegisterSuccessInfo"
              type="success"
              show-icon
              style="margin-bottom: 24px"
              :message="showRegisterSuccessInfo"
            />

            <a-form-item name="agentName" :label="false">
              <a-input
                v-model:value="formState.agentName"
                size="large"
                type="text"
                placeholder="请输入代理商名称"
              />
            </a-form-item>

            <a-form-item name="agentShortName" :label="false">
              <a-input
                v-model:value="formState.agentShortName"
                size="large"
                type="text"
                placeholder="请输入代理商简称"
              />
            </a-form-item>

            <a-form-item name="contactName" :label="false">
              <a-input
                v-model:value="formState.contactName"
                size="large"
                type="text"
                placeholder="请输入联系人姓名"
              />
            </a-form-item>

            <a-form-item name="contactTel" :label="false">
              <a-input
                v-model:value="formState.contactTel"
                size="large"
                type="text"
                placeholder="请输入联系人手机号"
              />
            </a-form-item>

            <a-form-item name="loginUsername" :label="false">
              <a-input
                v-model:value="formState.loginUsername"
                size="large"
                type="text"
                placeholder="请输入登录用户名"
              />
            </a-form-item>

            <a-form-item name="password" :label="false">
              <a-input-password
                v-model:value="formState.password"
                size="large"
                placeholder="请输入登录密码"
              />
            </a-form-item>

            <a-form-item name="confirmPassword" :label="false">
              <a-input-password
                v-model:value="formState.confirmPassword"
                size="large"
                placeholder="请确认登录密码"
              />
            </a-form-item>

            <a-row :gutter="16">
              <a-col class="gutter-row" :span="16">
                <a-form-item name="vercode" :label="false">
                  <a-input
                    v-model:value="formState.vercode"
                    size="large"
                    type="text"
                    placeholder="请输入验证码"
                  />
                </a-form-item>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <img
                  v-if="vercodeImgSrc"
                  :src="vercodeImgSrc"
                  class="getCaptcha"
                  @click="refVercode"
                />
              </a-col>
            </a-row>

            <a-form-item class="submit">
              <a-button
                size="large"
                type="primary"
                html-type="submit"
                class="register-button"
                :loading="registerBtnLoadingFlag"
              >
                注册
              </a-button>
            </a-form-item>

            <div class="user-login-other">
              <span>已有账号？</span>
              <router-link class="register" :to="{ name: 'login' }">
                立即登录
              </router-link>
            </div>
          </a-form>
        </div>

        <div class="footer" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { vercode, register } from '@/api/login'
import router from '@/router'
import { ref, onMounted, reactive, getCurrentInstance } from 'vue'

const { $infoBox } = getCurrentInstance()!.appContext.config.globalProperties

let registerBtnLoadingFlag = ref(false) // 注册按钮是否显示 加载状态
let showRegisterErrorInfo = ref('') // 是否显示注册错误面板信息
let showRegisterSuccessInfo = ref('') // 是否显示注册成功面板信息
let vercodeImgSrc = ref('') // 验证码图片
let vercodeToken = ref('') // 验证码验证token

let formRegister = ref()

let formState = reactive({
  agentName: '',
  agentShortName: '',
  contactName: '',
  contactTel: '',
  loginUsername: '',
  password: '',
  confirmPassword: '',
  vercode: '',
})

// 表单验证规则
const rules = {
  agentName: [
    { required: true, message: '请输入代理商名称', trigger: 'blur' },
    { max: 64, message: '代理商名称不能超过64个字符', trigger: 'blur' }
  ],
  agentShortName: [
    { required: true, message: '请输入代理商简称', trigger: 'blur' },
    { max: 32, message: '代理商简称不能超过32个字符', trigger: 'blur' }
  ],
  contactName: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
    { max: 32, message: '联系人姓名不能超过32个字符', trigger: 'blur' }
  ],
  contactTel: [
    { required: true, message: '请输入联系人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  loginUsername: [
    { required: true, message: '请输入登录用户名', trigger: 'blur' },
    { min: 4, max: 32, message: '登录用户名长度应在4-32个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入登录密码', trigger: 'blur' },
    { min: 6, max: 32, message: '密码长度应在6-32个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认登录密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value !== formState.password) {
          return Promise.reject('两次输入的密码不一致')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  vercode: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
}

onMounted(() => {
  refVercode()
})

function handleSubmit(values) {
  registerBtnLoadingFlag.value = true
  showRegisterErrorInfo.value = ''
  showRegisterSuccessInfo.value = ''

  let registerParams = {
    agentName: formState.agentName,
    agentShortName: formState.agentShortName,
    contactName: formState.contactName,
    contactTel: formState.contactTel,
    loginUsername: formState.loginUsername,
    password: formState.password,
    vercode: formState.vercode,
    vercodeToken: vercodeToken.value,
  }

  // 请求注册
  register(registerParams)
    .then((bizData) => {
      showRegisterSuccessInfo.value = '注册成功！请使用注册的账号密码登录。'
      // 3秒后跳转到登录页面
      setTimeout(() => {
        router.push({ path: '/login' })
      }, 3000)
    })
    .catch((err) => {
      showRegisterErrorInfo.value = err.msg || JSON.stringify(err)
      refVercode() // 刷新验证码
    })
    .finally(() => {
      registerBtnLoadingFlag.value = false
    })
}

// 获取验证码
function refVercode() {
  vercode()
    .then((bizData) => {
      vercodeImgSrc.value = bizData['imageBase64Data']
      vercodeToken.value = bizData['vercodeToken']
    })
    .catch((err) => {
      console.error('获取验证码失败:', err)
    })
}
</script>

<style lang="less" scoped>
.user-layout-wrapper {
  height: 100%;
  
  .container {
    width: 100%;
    min-height: 100%;
    background: #f0f2f5 url(~@/assets/background.svg) no-repeat 50%;
    background-size: 100%;
    padding: 110px 0 144px;
    position: relative;
  }

  .user-layout-content {
    width: 368px;
    margin: 0 auto;

    .top {
      text-align: center;

      .header {
        height: 44px;
        line-height: 44px;

        .logo {
          height: 44px;
          vertical-align: top;
          margin-right: 16px;
          border-style: none;
        }
      }

      .desc {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        margin-top: 12px;
        margin-bottom: 40px;

        .logo {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }
      }
    }

    .main {
      min-height: 380px;

      .user-layout-register {
        .ant-form-item-label {
          display: none;
        }

        .getCaptcha {
          display: block;
          width: 100%;
          height: 40px;
          cursor: pointer;
        }

        .register-button {
          width: 100%;
        }

        .user-login-other {
          text-align: center;
          margin-top: 24px;
          line-height: 22px;

          .register {
            color: #1890ff;
          }
        }
      }
    }
  }
}
</style>
