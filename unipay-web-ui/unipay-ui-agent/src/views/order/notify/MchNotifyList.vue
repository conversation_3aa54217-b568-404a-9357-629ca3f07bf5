<template>
  <page-header-wrapper>
    <a-card>
      <div class="table-page-search-wrapper">
        <a-form layout="inline" class="table-head-ground">
          <div class="table-page-search-option">
            <a-space size="8">
              <a-input v-model:value="searchData.orderId" placeholder="订单号" />
              <a-input v-model:value="searchData.mchNo" placeholder="商户号" />
              <a-input v-model:value="searchData.appId" placeholder="应用ID" />
              <a-select
                v-model:value="searchData.orderType"
                placeholder="订单类型"
                style="width: 120px"
                allowClear
              >
                <a-select-option :value="1">支付</a-select-option>
                <a-select-option :value="2">退款</a-select-option>
                <a-select-option :value="3">转账</a-select-option>
              </a-select>
              <a-select
                v-model:value="searchData.state"
                placeholder="通知状态"
                style="width: 120px"
                allowClear
              >
                <a-select-option :value="1">通知中</a-select-option>
                <a-select-option :value="2">通知成功</a-select-option>
                <a-select-option :value="3">通知失败</a-select-option>
              </a-select>
              <a-range-picker
                v-model:value="vdata.dateRange"
                :disabled-date="disabledDate"
                format="YYYY-MM-DD"
                @change="onChange"
              />
            </a-space>
            <span class="table-page-search-submitButtons">
              <a-button type="primary" :loading="vdata.btnLoading" @click="queryFunc">
                查询
              </a-button>
              <a-button style="margin-left: 8px" @click="searchData = {}">重置</a-button>
            </span>
          </div>
        </a-form>
      </div>

      <!-- 列表渲染 -->
      <JeepayTable
        @btnLoadClose="vdata.btnLoading = false"
        ref="infoTable"
        :closable="true"
        :initData="true"
        :reqTableDataFunc="reqTableDataFunc"
        :tableColumns="vdata.tableColumns"
        :searchData="vdata.searchData"
        :rowSelection="rowSelection"
        rowKey="orderId"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'state'">
            <a-tag
              :key="record.orderId"
              :color="record.state === 1 ? 'orange' : record.state === 2 ? 'green' : 'volcano'"
            >
              <template v-if="record.state == 1">通知中</template>
              <template v-else-if="record.state == 2">通知成功</template>
              <template v-else-if="record.state == 3">通知失败</template>
              <template v-else>未知</template>
            </a-tag>
          </template>
          <template v-if="column.key === 'orderType'">
            <a-tag v-if="record.orderType == 1" color="green">支付</a-tag>
            <a-tag v-else-if="record.orderType == 2" color="volcano">退款</a-tag>
            <a-tag v-else-if="record.orderType == 3" color="blue">转账</a-tag>
            <a-tag v-else color="orange">未知</a-tag>
          </template>
          <template v-if="column.key === 'op'">
            <!-- 操作列插槽 -->
            <JeepayTableColumns>
              <a-button
                type="link"
                v-if="(proxy as any)?.$access('ENT_MCH_NOTIFY_VIEW')"
                @click="detailFunc(record.notifyId)"
              >
                详情
              </a-button>
              <a-button
                type="link"
                v-if="(proxy as any)?.$access('ENT_MCH_NOTIFY_RESEND') && record.state === 3"
                @click="resendFunc(record.notifyId)"
              >
                重发通知
              </a-button>
            </JeepayTableColumns>
          </template>
        </template>
      </JeepayTable>
    </a-card>
    <!-- 日志详情抽屉 -->
    <template>
      <a-drawer
        width="40%"
        placement="right"
        :closable="true"
        v-model:open="vdata.open"
        @close="onClose"
      >
        <template #title>
          <span>通知详情</span>
        </template>
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="通知ID">
            {{ vdata.detailData.notifyId }}
          </a-descriptions-item>
          <a-descriptions-item label="订单号">
            {{ vdata.detailData.orderId }}
          </a-descriptions-item>
          <a-descriptions-item label="商户订单号">
            {{ vdata.detailData.mchOrderNo }}
          </a-descriptions-item>
          <a-descriptions-item label="商户号">
            {{ vdata.detailData.mchNo }}
          </a-descriptions-item>
          <a-descriptions-item label="应用ID">
            {{ vdata.detailData.appId }}
          </a-descriptions-item>
          <a-descriptions-item label="订单类型">
            <a-tag v-if="vdata.detailData.orderType == 1" color="green">支付</a-tag>
            <a-tag v-else-if="vdata.detailData.orderType == 2" color="volcano">退款</a-tag>
            <a-tag v-else-if="vdata.detailData.orderType == 3" color="blue">转账</a-tag>
            <a-tag v-else color="orange">未知</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="通知状态">
            <a-tag
              :color="vdata.detailData.state === 1 ? 'orange' : vdata.detailData.state === 2 ? 'green' : 'volcano'"
            >
              <template v-if="vdata.detailData.state == 1">通知中</template>
              <template v-else-if="vdata.detailData.state == 2">通知成功</template>
              <template v-else-if="vdata.detailData.state == 3">通知失败</template>
              <template v-else>未知</template>
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="通知地址">
            {{ vdata.detailData.notifyUrl }}
          </a-descriptions-item>
          <a-descriptions-item label="通知次数">
            {{ vdata.detailData.notifyCount }} / {{ vdata.detailData.notifyCountLimit }}
          </a-descriptions-item>
          <a-descriptions-item label="最后通知时间">
            {{ vdata.detailData.lastNotifyTime }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ vdata.detailData.createdAt }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ vdata.detailData.updatedAt }}
          </a-descriptions-item>
          <a-descriptions-item label="响应结果">
            <pre style="white-space: pre-wrap; word-break: break-all;">{{ vdata.detailData.resResult }}</pre>
          </a-descriptions-item>
        </a-descriptions>
      </a-drawer>
    </template>
  </page-header-wrapper>
</template>

<script setup lang="ts">
import { API_URL_MCH_NOTIFY_LIST, req, mchNotifyResend } from '@/api/manage'
import { ref, reactive, getCurrentInstance } from 'vue'
import moment from 'moment'

const { proxy } = getCurrentInstance() as any
const { $infoBox } = proxy

// 列表查询接口
const rowSelection = ref()

// 定义搜索数据类型
interface SearchData {
  orderId?: string
  mchNo?: string
  appId?: string
  orderType?: number
  state?: number
  createdStart?: string
  createdEnd?: string
}

const searchData = reactive<SearchData>({})

// 定义详情数据类型
interface DetailData {
  notifyId?: number
  orderId?: string
  mchOrderNo?: string
  mchNo?: string
  appId?: string
  orderType?: number
  state?: number
  notifyUrl?: string
  notifyCount?: number
  notifyCountLimit?: number
  lastNotifyTime?: string
  createdAt?: string
  updatedAt?: string
  resResult?: string
}

const vdata = reactive({
  btnLoading: false,
  // 表格公共参数（必须）
  tableColumns: [
    { key: 'notifyId', dataIndex: 'notifyId', title: '通知ID' },
    { key: 'orderId', dataIndex: 'orderId', title: '订单号' },
    { key: 'mchOrderNo', dataIndex: 'mchOrderNo', title: '商户订单号' },
    { key: 'mchNo', dataIndex: 'mchNo', title: '商户号' },
    { key: 'appId', dataIndex: 'appId', title: '应用ID' },
    { key: 'orderType', dataIndex: 'orderType', title: '订单类型', width: 80 },
    { key: 'state', dataIndex: 'state', title: '通知状态', width: 80 },
    { key: 'notifyCount', dataIndex: 'notifyCount', title: '通知次数', width: 80 },
    { key: 'lastNotifyTime', dataIndex: 'lastNotifyTime', title: '最后通知时间', width: 150 },
    { key: 'createdAt', dataIndex: 'createdAt', title: '创建时间', width: 150 },
    {
      key: 'op',
      title: '操作',
      width: 120,
      fixed: 'right'
    }
  ],
  searchData: searchData,
  open: false,
  detailData: {} as DetailData,
  dateRange: [] as any[]
})

const infoTable = ref()

function queryFunc() {
  vdata.btnLoading = true
  infoTable.value.refesh()
}

// 请求table接口数据
function reqTableDataFunc(params: any) {
  return req.list(API_URL_MCH_NOTIFY_LIST, params)
}

function searchFunc() {
  vdata.searchData = searchData
}

function detailFunc(recordId: string | number) {
  req.getById(API_URL_MCH_NOTIFY_LIST, recordId).then((res: DetailData) => {
    vdata.detailData = res
  })
  vdata.open = true
}

function onChange(_date: any, dateString: string[]) {
  vdata.searchData.createdStart = dateString[0] // 开始时间
  vdata.searchData.createdEnd = dateString[1] // 结束时间
}

function disabledDate(current: any) {
  // 今日之后日期不可选
  return current && current > moment().endOf('day')
}

function onClose() {
  vdata.open = false
}

function resendFunc(notifyId: number) {
  // 重发通知
  $infoBox.confirmPrimary('确认重发通知？', '', () => {
    mchNotifyResend(notifyId).then((_res: any) => {
      $infoBox.message.success('任务更新成功，请稍后查看最新状态！')
      searchFunc()
    })
  })
}
</script>
