#!/bin/bash

# UniPay UI构建脚本
# 支持编译单个模块或所有模块，并自动将构建结果同步到服务端static目录

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
MODULE_NAME=""
BUILD_ALL=false
AUTO_SYNC=true  # 默认自动同步

# 支持的UI模块及其对应的目标目录
declare -A UI_MODULES
UI_MODULES["agent"]="unipay-ui-agent"
UI_MODULES["manager"]="unipay-ui-manager"
UI_MODULES["merchant"]="unipay-ui-merchant"
UI_MODULES["cashier"]="unipay-ui-cashier"

# 模块与目标目录映射
declare -A MODULE_DEST_MAP
MODULE_DEST_MAP["agent"]="sys-agent/src/main/resources/static"
MODULE_DEST_MAP["manager"]="sys-manager/src/main/resources/static"
MODULE_DEST_MAP["merchant"]="sys-merchant/src/main/resources/static"
MODULE_DEST_MAP["cashier"]="sys-payment/src/main/resources/static"

# 所有模块列表
ALL_MODULES=("agent" "manager" "merchant" "cashier")

# 打印使用说明
usage() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -m, --module MODULE     指定要构建的UI模块 (agent, manager, merchant, cashier)"
    echo "  -a, --all              构建所有UI模块"
    echo "  -l, --list             列出所有支持的UI模块"
    echo "  -n, --no-sync          构建后不自动同步到服务端目录"
    echo "  -h, --help             显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -m agent            # 构建代理商UI模块并同步"
    echo "  $0 --module manager    # 构建运营平台UI模块并同步"
    echo "  $0 -a                  # 构建所有UI模块并同步"
    echo "  $0 -m agent -n         # 构建代理商UI模块但不自动同步"
    echo "  $0 -l                  # 列出所有支持的UI模块"
    exit 1
}

# 列出所有支持的模块
list_modules() {
    echo -e "${BLUE}支持的UI模块:${NC}"
    for module in "${ALL_MODULES[@]}"; do
        echo "  $module (${UI_MODULES[$module]})"
    done
}

# 同步构建结果到服务端目录
sync_to_static() {
    local module_key=$1
    local module_dir=${UI_MODULES[$module_key]}
    local dest_dir=${MODULE_DEST_MAP[$module_key]}
    local src_dir="unipay-web-ui/$module_dir/dist"
    
    if [ -z "$dest_dir" ]; then
        echo -e "${RED}错误: 未找到模块 $module_key 的目标目录映射${NC}"
        return 1
    fi
    
    if [ ! -d "$src_dir" ]; then
        echo -e "${RED}错误: 源目录 $src_dir 不存在${NC}"
        return 1
    fi
    
    echo -e "${BLUE}正在同步 $module_dir 构建结果到 $dest_dir ...${NC}"
    
    # 检查目标目录是否存在，不存在则创建
    if [ ! -d "$dest_dir" ]; then
        echo -e "${YELLOW}目标目录 $dest_dir 不存在，正在创建...${NC}"
        mkdir -p "$dest_dir" || { echo -e "${RED}❌ 创建目录 $dest_dir 失败${NC}"; return 1; }
    fi
    
    # 拷贝文件（-a 保留属性，-v 显示过程）
    echo -e "${BLUE}拷贝文件中...${NC}"
    rsync -av --delete "$src_dir/" "$dest_dir/" || { echo -e "${RED}❌ 拷贝 $src_dir 到 $dest_dir 失败${NC}"; return 1; }
    
    echo -e "${GREEN}✅ $module_dir 构建结果已同步到 $dest_dir${NC}"
    return 0
}

# 构建单个模块
build_module() {
    local module_key=$1
    local module_dir=${UI_MODULES[$module_key]}
    
    if [ -z "$module_dir" ]; then
        echo -e "${RED}错误: 不支持的模块 '$module_key'${NC}"
        list_modules
        exit 1
    fi
    
    if [ ! -d "unipay-web-ui/$module_dir" ]; then
        echo -e "${RED}错误: 模块目录 unipay-web-ui/$module_dir 不存在${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}开始构建 $module_dir 模块...${NC}"
    
    cd "unipay-web-ui/$module_dir" || exit 1
    
    if [ ! -f "package.json" ]; then
        echo -e "${RED}错误: 未找到 package.json 文件${NC}"
        cd ../..
        exit 1
    fi
    
    # 检查是否存在npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}错误: 未找到 npm 命令，请确保已安装 Node.js${NC}"
        cd ../..
        exit 1
    fi
    
    echo -e "${BLUE}安装依赖...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 依赖安装失败${NC}"
        cd ../..
        exit 1
    fi
    
    echo -e "${BLUE}开始构建...${NC}"
    npm run build
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 构建失败${NC}"
        cd ../..
        exit 1
    fi
    
    echo -e "${GREEN}$module_dir 模块构建完成!${NC}"
    cd ../..
    
    # 自动同步到服务端目录
    if [ "$AUTO_SYNC" = true ]; then
        sync_to_static "$module_key"
    else
        echo -e "${YELLOW}跳过同步到服务端目录${NC}"
    fi
}

# 构建所有模块
build_all() {
    echo -e "${GREEN}开始构建所有UI模块...${NC}"
    
    for module_key in "${ALL_MODULES[@]}"; do
        echo -e "${YELLOW}----------------------------------------${NC}"
        build_module "$module_key"
    done
    
    echo -e "${YELLOW}----------------------------------------${NC}"
    echo -e "${GREEN}所有UI模块构建完成!${NC}"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--module)
            MODULE_NAME="$2"
            BUILD_ALL=false
            shift 2
            ;;
        -a|--all)
            BUILD_ALL=true
            shift
            ;;
        -l|--list)
            list_modules
            exit 0
            ;;
        -n|--no-sync)
            AUTO_SYNC=false
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            usage
            ;;
    esac
done

# 检查是否在项目根目录
if [ ! -d "unipay-web-ui" ]; then
    echo -e "${RED}错误: 未找到 unipay-web-ui 目录，请在项目根目录执行此脚本${NC}"
    exit 1
fi

# 检查rsync命令是否存在
if ! command -v rsync &> /dev/null; then
    echo -e "${RED}错误: 未找到 rsync 命令，请先安装 rsync${NC}"
    exit 1
fi

# 根据参数执行相应操作
if [ "$BUILD_ALL" = true ]; then
    build_all
elif [ -n "$MODULE_NAME" ]; then
    build_module "$MODULE_NAME"
else
    echo -e "${YELLOW}未指定模块，构建所有UI模块...${NC}"
    build_all
fi