<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏充值系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .game-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .coin-balance {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .recharge-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 15px;
        }
        .amount-btn {
            border: 2px solid #667eea;
            color: #667eea;
            background: white;
            border-radius: 10px;
            padding: 0.8rem 1.5rem;
            margin: 0.3rem;
            transition: all 0.3s;
        }
        .amount-btn:hover, .amount-btn.active {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="game-header text-center">
        <div class="container">
            <h1>游戏充值系统</h1>
            <p class="mb-0">安全便捷的游戏币充值服务</p>
        </div>
    </div>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-4">
                <div class="coin-balance text-center">
                    <h5>用户信息</h5>
                    <div class="mt-3">
                        <h6>用户名: <span th:text="${user.username}">testuser</span></h6>
                        <h4><span th:text="${user.coinBalance}">0</span> 游戏币</h4>
                        <small>累计充值: ¥<span th:text="${user.totalRecharge}">0</span></small>
                    </div>
                </div>

                <div class="card recharge-card mt-3">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">快速充值</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <button class="btn amount-btn" onclick="selectAmount(1)">¥1 = 100币</button>
                            <button class="btn amount-btn" onclick="selectAmount(5)">¥5 = 500币</button>
                            <button class="btn amount-btn" onclick="selectAmount(10)">¥10 = 1000币</button>
                            <button class="btn amount-btn" onclick="selectAmount(20)">¥20 = 2000币</button>
                        </div>
                        
                        <div class="input-group mb-3">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" id="customAmount" placeholder="自定义金额" min="0.01" step="0.01">
                        </div>
                        
                        <button class="btn btn-primary w-100" onclick="createRecharge()">
                            生成支付宝二维码
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <!-- 二维码显示区域 -->
                <div class="card recharge-card mb-3" id="qrCodeCard" style="display: none;">
                    <div class="card-header bg-warning text-white">
                        <h6 class="mb-0">支付二维码</h6>
                    </div>
                    <div class="card-body text-center">
                        <div id="qrCodeContainer"></div>
                        <p class="mt-3 text-muted">请使用支付宝扫描上方二维码完成支付</p>
                        <button class="btn btn-secondary" onclick="hideQrCode()">关闭二维码</button>
                    </div>
                </div>

                <div class="card recharge-card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">充值记录</h6>
                    </div>
                    <div class="card-body">
                        <div th:each="order : ${orders}" class="border-bottom py-2">
                            <div class="row">
                                <div class="col-md-3">
                                    <small>订单号</small><br>
                                    <code th:text="${order.gameOrderNo}">G123456789</code>
                                </div>
                                <div class="col-md-2">
                                    <small>金额</small><br>
                                    <strong>¥<span th:text="${order.amount}">10.00</span></strong>
                                </div>
                                <div class="col-md-2">
                                    <small>游戏币</small><br>
                                    <span th:text="${order.coinAmount}">1000</span>
                                </div>
                                <div class="col-md-2">
                                    <small>状态</small><br>
                                    <span th:if="${order.status == 0}" class="badge bg-warning">待支付</span>
                                    <span th:if="${order.status == 1}" class="badge bg-success">已完成</span>
                                    <span th:if="${order.status == 2}" class="badge bg-danger">支付失败</span>
                                </div>
                                <div class="col-md-3">
                                    <small>时间</small><br>
                                    <small th:text="${#temporals.format(order.createTime, 'MM-dd HH:mm')}">12-25 14:30</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const username = /*[[${user.username}]]*/ 'testuser';

        function selectAmount(amount) {
            document.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            document.getElementById('customAmount').value = amount;
        }

        function createRecharge() {
            const amount = document.getElementById('customAmount').value;
            if (!amount || amount <= 0) {
                alert('请输入有效的充值金额');
                return;
            }

            fetch('/recharge/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `username=${username}&amount=${amount}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const order = data.data;
                    if (order.qrCodeUrl) {
                        showQrCode(order.qrCodeUrl, order.gameOrderNo);
                    } else {
                        alert('充值订单创建成功！订单号: ' + order.gameOrderNo);
                        location.reload();
                    }
                } else {
                    alert('创建订单失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
            });
        }

        function showQrCode(qrCodeUrl, orderNo) {
            const qrCodeContainer = document.getElementById('qrCodeContainer');
            qrCodeContainer.innerHTML = `
                <div class="mb-3">
                    <h5>订单号: ${orderNo}</h5>
                </div>
                <div class="mb-3">
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCodeUrl)}" 
                         alt="支付二维码" class="img-fluid" style="max-width: 200px;">
                </div>
                <div class="mb-2">
                    <small class="text-muted">二维码内容: ${qrCodeUrl}</small>
                </div>
            `;
            document.getElementById('qrCodeCard').style.display = 'block';
            
            // 滚动到二维码区域
            document.getElementById('qrCodeCard').scrollIntoView({ behavior: 'smooth' });
        }

        function hideQrCode() {
            document.getElementById('qrCodeCard').style.display = 'none';
            location.reload(); // 刷新页面更新充值记录
        }
    </script>
</body>
</html>