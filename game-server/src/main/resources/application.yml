server:
  port: 8088
  servlet:
    context-path: /

spring:
  application:
    name: game-server
  
  # H2 数据库配置
  datasource:
    url: jdbc:h2:mem:gamedb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  # H2 控制台
  h2:
    console:
      enabled: true
      path: /h2-console

# 支付网关配置
payment:
  gateway:
    url: http://47.110.37.157:9216  # sys-payment 服务地址
    mch-no: M1757512463      # 商户号
    app-id: 68c1830fd138c62623a963c0  # 应用ID
    app-secret: rJsYlPJUNTU9TLhhqvbk7koQkiGhBou7UHkrvFwqs0TBIm7MoGNdqoMOMTVtGVezO0GqBfnkInk50sKxt87y5IK6TMC1GimwvFfhSqDWNOWy4xuXRGSLijXijVRZJYsG  # 应用密钥
    notify-url: http://p882e496.natappfree.cc/api/payment/notify  # 支付回调地址

# 游戏配置
game:
  coin-rate: 100  # 1元 = 100游戏币

# 日志配置
logging:
  level:
    com.game: debug
    org.springframework.web: debug