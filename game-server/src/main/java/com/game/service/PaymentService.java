package com.game.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.game.config.PaymentConfig;
import com.game.util.PaymentUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付服务
 */
@Slf4j
@Service
public class PaymentService {

    @Autowired
    private PaymentConfig paymentConfig;

    /**
     * 创建支付订单
     */
    public JSONObject createPayOrder(String gameOrderNo, BigDecimal amount, String subject, String body) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("mchNo", paymentConfig.getMchNo());
            params.put("appId", paymentConfig.getAppId());
            params.put("mchOrderNo", gameOrderNo);
            params.put("wayCode", "ALI_QR"); // 支付宝二维码
            params.put("amount", amount.multiply(new BigDecimal("100")).longValue()); // 转换为分
            params.put("currency", "cny");
            params.put("clientIp", "127.0.0.1");
            params.put("subject", subject);
            params.put("body", body);
            params.put("notifyUrl", paymentConfig.getNotifyUrl());
            params.put("returnUrl", "http://localhost:8080/recharge/success");
            params.put("channelExtra", "{}");
            params.put("extParam", "{}");
            params.put("reqTime", System.currentTimeMillis());
            params.put("version", "1.0");
            params.put("signType", "MD5");

            // 生成签名
            String sign = PaymentUtil.generateSign(params, paymentConfig.getAppSecret());
            params.put("sign", sign);

            // 发送HTTP请求
            String url = paymentConfig.getUrl() + "/api/pay/unifiedOrder";
            String response = sendHttpPost(url, JSON.toJSONString(params));

            log.info("支付订单创建请求: {}", JSON.toJSONString(params));
            log.info("支付订单创建响应: {}", response);

            return JSON.parseObject(response);

        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            throw new RuntimeException("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付订单状态
     */
    public JSONObject queryPayOrder(String payOrderId) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("mchNo", paymentConfig.getMchNo());
            params.put("appId", paymentConfig.getAppId());
            params.put("payOrderId", payOrderId);
            params.put("reqTime", System.currentTimeMillis());
            params.put("version", "1.0");
            params.put("signType", "MD5");

            // 生成签名
            String sign = PaymentUtil.generateSign(params, paymentConfig.getAppSecret());
            params.put("sign", sign);

            // 发送HTTP请求
            String url = paymentConfig.getUrl() + "/api/pay/query/" + payOrderId;
            String response = sendHttpPost(url, JSON.toJSONString(params));

            log.info("查询支付订单请求: {}", JSON.toJSONString(params));
            log.info("查询支付订单响应: {}", response);

            return JSON.parseObject(response);

        } catch (Exception e) {
            log.error("查询支付订单失败", e);
            throw new RuntimeException("查询支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 发送HTTP POST请求
     */
    private String sendHttpPost(String url, String jsonData) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setEntity(new StringEntity(jsonData, ContentType.APPLICATION_JSON));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                return EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        }
    }
}