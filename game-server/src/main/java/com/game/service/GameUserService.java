package com.game.service;

import com.game.entity.GameUser;
import com.game.repository.GameUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 游戏用户服务
 */
@Slf4j
@Service
public class GameUserService {

    @Autowired
    private GameUserRepository gameUserRepository;

    /**
     * 根据用户名获取用户
     */
    public GameUser getUserByUsername(String username) {
        return gameUserRepository.findByUsername(username).orElse(null);
    }

    /**
     * 创建用户
     */
    public GameUser createUser(String username, String nickname) {
        if (gameUserRepository.existsByUsername(username)) {
            throw new RuntimeException("用户名已存在");
        }

        GameUser user = new GameUser();
        user.setUsername(username);
        user.setNickname(nickname);
        user.setCoinBalance(BigDecimal.ZERO);
        user.setTotalRecharge(BigDecimal.ZERO);
        user.setStatus(1);

        return gameUserRepository.save(user);
    }

    /**
     * 获取或创建用户
     */
    public GameUser getOrCreateUser(String username) {
        GameUser user = getUserByUsername(username);
        if (user == null) {
            user = createUser(username, username);
            log.info("创建新用户: {}", username);
        }
        return user;
    }

    /**
     * 增加用户游戏币
     */
    @Transactional
    public boolean addCoinBalance(Long userId, BigDecimal coinAmount, BigDecimal rechargeAmount) {
        int result = gameUserRepository.addCoinBalance(userId, coinAmount, rechargeAmount);
        if (result > 0) {
            log.info("用户 {} 充值成功，获得游戏币: {}，充值金额: {}", userId, coinAmount, rechargeAmount);
            return true;
        }
        return false;
    }

    /**
     * 根据ID获取用户
     */
    public GameUser getUserById(Long userId) {
        Optional<GameUser> userOpt = gameUserRepository.findById(userId);
        return userOpt.orElse(null);
    }
}