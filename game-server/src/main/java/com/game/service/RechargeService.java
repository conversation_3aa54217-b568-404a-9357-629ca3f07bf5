package com.game.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.game.config.GameConfig;
import com.game.entity.GameUser;
import com.game.entity.RechargeOrder;
import com.game.repository.RechargeOrderRepository;
import com.game.util.PaymentUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 充值服务
 */
@Slf4j
@Service
public class RechargeService {

    @Autowired
    private RechargeOrderRepository rechargeOrderRepository;

    @Autowired
    private GameUserService gameUserService;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private GameConfig gameConfig;

    /**
     * 创建充值订单
     */
    @Transactional
    public JSONObject createRechargeOrder(String username, BigDecimal amount) {
        // 获取或创建用户
        GameUser user = gameUserService.getOrCreateUser(username);

        // 计算游戏币数量
        BigDecimal coinAmount = amount.multiply(new BigDecimal(gameConfig.getCoinRate()));

        // 创建充值订单
        RechargeOrder order = new RechargeOrder();
        order.setGameOrderNo(PaymentUtil.generateOrderNo());
        order.setUserId(user.getId());
        order.setUsername(username);
        order.setAmount(amount);
        order.setCoinAmount(coinAmount);
        order.setStatus(RechargeOrder.STATUS_PENDING);
        order.setPayWay("ALI_QR");

        // 保存订单
        order = rechargeOrderRepository.save(order);

        // 调用支付网关创建支付订单
        try {
            String subject = "游戏充值 - " + coinAmount + "游戏币";
            String body = "用户 " + username + " 充值 " + amount + "元，获得 " + coinAmount + " 游戏币";
            
            JSONObject payResult = paymentService.createPayOrder(order.getGameOrderNo(), amount, subject, body);
            
            if (payResult.getInteger("code") == 0) {
                JSONObject data = payResult.getJSONObject("data");
                order.setPayOrderId(data.getString("payOrderId"));
                order = rechargeOrderRepository.save(order);
                log.info("充值订单创建成功: {}", order.getGameOrderNo());
                
                // 构造返回结果，包含订单信息和二维码URL
                JSONObject result = new JSONObject();
                result.put("id", order.getId());
                result.put("gameOrderNo", order.getGameOrderNo());
                result.put("payOrderId", order.getPayOrderId());
                result.put("amount", order.getAmount());
                result.put("coinAmount", order.getCoinAmount());
                result.put("status", order.getStatus());
                result.put("qrCodeUrl", data.getString("payData")); // 二维码URL
                result.put("createTime", order.getCreateTime());
                
                return result;
            } else {
                throw new RuntimeException("支付订单创建失败: " + payResult.getString("msg"));
            }
        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            throw new RuntimeException("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 处理支付回调
     */
    @Transactional
    public boolean handlePaymentCallback(String payOrderId, String callbackData) {
        try {
            log.info("处理支付回调: payOrderId={}, data={}", payOrderId, callbackData);

            // 查找充值订单
            RechargeOrder order = rechargeOrderRepository.findByPayOrderId(payOrderId).orElse(null);
            if (order == null) {
                log.warn("未找到充值订单: {}", payOrderId);
                return false;
            }

            // 检查订单状态
            if (order.getStatus() != RechargeOrder.STATUS_PENDING) {
                log.warn("订单状态异常: {}, status={}", payOrderId, order.getStatus());
                return true; // 已处理过，返回成功
            }

            // 解析回调数据
            JSONObject callbackJson = JSON.parseObject(callbackData);
            Integer state = callbackJson.getInteger("state");

            if (state != null && state == 2) { // 支付成功
                // 更新订单状态
                order.setStatus(RechargeOrder.STATUS_SUCCESS);
                order.setPayTime(LocalDateTime.now());
                order.setCallbackData(callbackData);
                rechargeOrderRepository.save(order);

                // 增加用户游戏币
                boolean success = gameUserService.addCoinBalance(order.getUserId(), order.getCoinAmount(), order.getAmount());
                if (success) {
                    log.info("充值成功: 用户={}, 订单={}, 金额={}, 游戏币={}", 
                            order.getUsername(), order.getGameOrderNo(), order.getAmount(), order.getCoinAmount());
                    return true;
                } else {
                    log.error("增加游戏币失败: {}", order.getGameOrderNo());
                    return false;
                }
            } else {
                // 支付失败
                order.setStatus(RechargeOrder.STATUS_FAILED);
                order.setCallbackData(callbackData);
                order.setRemark("支付失败，状态码: " + state);
                rechargeOrderRepository.save(order);
                log.info("支付失败: {}", order.getGameOrderNo());
                return true;
            }

        } catch (Exception e) {
            log.error("处理支付回调失败", e);
            return false;
        }
    }

    /**
     * 根据订单号获取订单
     */
    public RechargeOrder getOrderByGameOrderNo(String gameOrderNo) {
        return rechargeOrderRepository.findByGameOrderNo(gameOrderNo).orElse(null);
    }

    /**
     * 根据用户名获取订单列表
     */
    public List<RechargeOrder> getOrdersByUsername(String username) {
        return rechargeOrderRepository.findByUsernameOrderByCreateTimeDesc(username);
    }

    /**
     * 查询支付订单状态
     */
    public JSONObject queryPayOrderStatus(String gameOrderNo) {
        RechargeOrder order = getOrderByGameOrderNo(gameOrderNo);
        if (order == null || order.getPayOrderId() == null) {
            return null;
        }
        return paymentService.queryPayOrder(order.getPayOrderId());
    }
}