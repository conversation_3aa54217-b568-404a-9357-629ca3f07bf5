package com.game;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 游戏服务器启动类
 */
@SpringBootApplication
public class GameServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(GameServerApplication.class, args);
        System.out.println("=================================");
        System.out.println("游戏充值服务器启动成功！");
        System.out.println("访问地址: http://localhost:8088");
        System.out.println("H2控制台: http://localhost:8088/h2-console");
        System.out.println("=================================");
    }
}