package com.game.util;

import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;

/**
 * 支付工具类
 */
public class PaymentUtil {

    /**
     * 生成签名
     */
    public static String generateSign(Map<String, Object> params, String appSecret) {
        try {
            // 1. 参数排序
            TreeMap<String, Object> sortedParams = new TreeMap<>(params);
            
            // 2. 拼接参数字符串
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
                if (entry.getValue() != null && !"sign".equals(entry.getKey())) {
                    sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                }
            }
            
            // 3. 添加密钥
            sb.append("key=").append(appSecret);
            
            // 4. MD5加密
            String signStr = sb.toString();
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(signStr.getBytes("UTF-8"));
            
            // 5. 转换为大写十六进制
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString().toUpperCase();
        } catch (Exception e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * 验证签名
     */
    public static boolean verifySign(Map<String, Object> params, String appSecret, String sign) {
        String calculatedSign = generateSign(params, appSecret);
        return calculatedSign.equals(sign);
    }

    /**
     * 生成订单号
     */
    public static String generateOrderNo() {
        return "G" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
    }
}