package com.game.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.game.config.PaymentConfig;
import com.game.service.RechargeService;
import com.game.util.PaymentUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付回调控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/payment")
public class PaymentCallbackController {

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private PaymentConfig paymentConfig;

    /**
     * 支付回调接口
     */
    @PostMapping("/notify")
    public String paymentNotify(@RequestBody String requestBody) {
        log.info("收到支付回调: {}", requestBody);
        
        try {
            // 解析URL编码的表单数据
            Map<String, Object> params = parseFormData(requestBody);
            
            // 验证签名
            String sign = (String) params.get("sign");
            if (sign == null) {
                log.error("支付回调缺少签名");
                return "fail";
            }
            
            // 创建验证签名用的参数Map（不包含sign）
            Map<String, Object> signParams = new HashMap<>(params);
            signParams.remove("sign");
            
            boolean signValid = PaymentUtil.verifySign(signParams, paymentConfig.getAppSecret(), sign);
            if (!signValid) {
                log.error("支付回调签名验证失败");
                return "fail";
            }

            // 获取订单信息
            String payOrderId = (String) params.get("payOrderId");
            if (payOrderId == null) {
                log.error("支付回调缺少payOrderId");
                return "fail";
            }

            // 将参数转换为JSON字符串传递给服务层
            JSONObject callbackJson = new JSONObject(params);
            boolean success = rechargeService.handlePaymentCallback(payOrderId, callbackJson.toJSONString());
            
            if (success) {
                log.info("支付回调处理成功: {}", payOrderId);
                return "success";
            } else {
                log.error("支付回调处理失败: {}", payOrderId);
                return "fail";
            }
            
        } catch (Exception e) {
            log.error("处理支付回调异常", e);
            return "fail";
        }
    }

    /**
     * 解析URL编码的表单数据
     */
    private Map<String, Object> parseFormData(String formData) {
        Map<String, Object> params = new HashMap<>();
        if (formData == null || formData.trim().isEmpty()) {
            return params;
        }
        
        try {
            String[] pairs = formData.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    String key = java.net.URLDecoder.decode(keyValue[0], "UTF-8");
                    String value = java.net.URLDecoder.decode(keyValue[1], "UTF-8");
                    params.put(key, value);
                }
            }
        } catch (Exception e) {
            log.error("解析表单数据失败", e);
        }
        
        return params;
    }

    /**
     * 测试回调接口
     */
    @PostMapping("/test-notify")
    public String testNotify(@RequestParam String payOrderId, 
                           @RequestParam(defaultValue = "2") Integer state) {
        log.info("测试支付回调: payOrderId={}, state={}", payOrderId, state);
        
        try {
            // 构造测试回调数据
            JSONObject testData = new JSONObject();
            testData.put("payOrderId", payOrderId);
            testData.put("mchOrderNo", "test_order");
            testData.put("state", state);
            testData.put("amount", 100L);
            testData.put("mchNo", paymentConfig.getMchNo());
            testData.put("appId", paymentConfig.getAppId());
            testData.put("reqTime", System.currentTimeMillis());
            
            // 生成签名
            Map<String, Object> params = new HashMap<>();
            for (String key : testData.keySet()) {
                params.put(key, testData.get(key));
            }
            String sign = PaymentUtil.generateSign(params, paymentConfig.getAppSecret());
            testData.put("sign", sign);
            
            // 处理回调
            boolean success = rechargeService.handlePaymentCallback(payOrderId, testData.toJSONString());
            
            return success ? "success" : "fail";
            
        } catch (Exception e) {
            log.error("测试支付回调异常", e);
            return "fail";
        }
    }
}