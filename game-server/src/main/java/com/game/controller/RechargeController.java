package com.game.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.game.entity.GameUser;
import com.game.entity.RechargeOrder;
import com.game.service.GameUserService;
import com.game.service.RechargeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 充值控制器
 */
@Slf4j
@Controller
@RequestMapping("/recharge")
public class RechargeController {

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private GameUserService gameUserService;

    /**
     * 充值页面
     */
    @GetMapping("")
    public String rechargePage(@RequestParam(defaultValue = "testuser") String username, Model model) {
        GameUser user = gameUserService.getOrCreateUser(username);
        List<RechargeOrder> orders = rechargeService.getOrdersByUsername(username);
        
        model.addAttribute("user", user);
        model.addAttribute("orders", orders);
        return "recharge";
    }

    /**
     * 创建充值订单
     */
    @PostMapping("/create")
    @ResponseBody
    public Map<String, Object> createRecharge(@RequestParam String username, 
                                            @RequestParam BigDecimal amount) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                result.put("success", false);
                result.put("message", "充值金额必须大于0");
                return result;
            }

            JSONObject orderData = rechargeService.createRechargeOrder(username, amount);
            
            result.put("success", true);
            result.put("message", "充值订单创建成功");
            result.put("data", orderData);
            
        } catch (Exception e) {
            log.error("创建充值订单失败", e);
            result.put("success", false);
            result.put("message", "创建充值订单失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 查询订单状态
     */
    @GetMapping("/query/{gameOrderNo}")
    @ResponseBody
    public Map<String, Object> queryOrder(@PathVariable String gameOrderNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            RechargeOrder order = rechargeService.getOrderByGameOrderNo(gameOrderNo);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            // 查询支付网关订单状态
            JSONObject payStatus = rechargeService.queryPayOrderStatus(gameOrderNo);
            
            result.put("success", true);
            result.put("order", order);
            result.put("payStatus", payStatus);
            
        } catch (Exception e) {
            log.error("查询订单失败", e);
            result.put("success", false);
            result.put("message", "查询订单失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 充值成功页面
     */
    @GetMapping("/success")
    public String successPage(@RequestParam(required = false) String orderNo, Model model) {
        if (orderNo != null) {
            RechargeOrder order = rechargeService.getOrderByGameOrderNo(orderNo);
            model.addAttribute("order", order);
        }
        return "success";
    }

    /**
     * 用户信息API
     */
    @GetMapping("/user/{username}")
    @ResponseBody
    public Map<String, Object> getUserInfo(@PathVariable String username) {
        Map<String, Object> result = new HashMap<>();
        try {
            GameUser user = gameUserService.getUserByUsername(username);
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            result.put("success", true);
            result.put("user", user);
            
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            result.put("success", false);
            result.put("message", "获取用户信息失败: " + e.getMessage());
        }
        return result;
    }
}