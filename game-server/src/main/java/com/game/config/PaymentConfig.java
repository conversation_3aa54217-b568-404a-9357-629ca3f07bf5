package com.game.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 支付配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "payment.gateway")
public class PaymentConfig {

    /**
     * 支付网关地址
     */
    private String url;

    /**
     * 商户号
     */
    private String mchNo;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 支付回调地址
     */
    private String notifyUrl;
}