package com.game.repository;

import com.game.entity.GameUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 游戏用户数据访问层
 */
@Repository
public interface GameUserRepository extends JpaRepository<GameUser, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<GameUser> findByUsername(String username);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 增加用户游戏币余额
     */
    @Modifying
    @Query("UPDATE GameUser u SET u.coinBalance = u.coinBalance + :amount, u.totalRecharge = u.totalRecharge + :rechargeAmount WHERE u.id = :userId")
    int addCoinBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount, @Param("rechargeAmount") BigDecimal rechargeAmount);
}