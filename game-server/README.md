# 游戏充值系统

这是一个完整的游戏充值系统示例，用于测试支付回调功能。

## 功能特性

- 🎮 游戏用户管理
- 💰 游戏币充值
- 📱 支付宝二维码支付
- 🔔 支付回调处理
- 📊 充值记录查询
- 🎯 实时状态更新

## 技术栈

- **后端**: Spring Boot 3.3.7 + JPA + H2
- **前端**: Thymeleaf + Bootstrap 5
- **支付**: 对接 sys-payment 支付网关
- **数据库**: H2 内存数据库

## 快速开始

### 1. 启动支付网关

确保 `sys-payment` 服务已启动并运行在 `http://localhost:9216`

### 2. 启动游戏服务器

```bash
cd game-server
mvn spring-boot:run
```

### 3. 访问系统

- 游戏充值页面: http://localhost:8080/recharge
- H2 数据库控制台: http://localhost:8080/h2-console

## 系统配置

### 支付网关配置 (application.yml)

```yaml
payment:
  gateway:
    url: http://localhost:9216          # 支付网关地址
    mch-no: M1621873433953             # 商户号
    app-id: 60cc09bce4b0f1c0b83761c9   # 应用ID
    app-secret: d0d10dd8a6f64d6d829271e60d9bf9ab  # 应用密钥
    notify-url: http://localhost:8088/api/payment/notify  # 回调地址
```

### 游戏配置

```yaml
game:
  coin-rate: 100  # 1元 = 100游戏币
```

## API 接口

### 充值相关

- `POST /recharge/create` - 创建充值订单
- `GET /recharge/query/{orderNo}` - 查询订单状态
- `GET /recharge/user/{username}` - 获取用户信息

### 支付回调

- `POST /api/payment/notify` - 支付成功回调
- `POST /api/payment/test-notify` - 测试回调接口

## 数据库表结构

### game_user (游戏用户表)

- id: 用户ID
- username: 用户名
- nickname: 昵称
- coin_balance: 游戏币余额
- total_recharge: 累计充值金额
- status: 用户状态
- create_time: 创建时间
- update_time: 更新时间

### recharge_order (充值订单表)

- id: 订单ID
- game_order_no: 游戏订单号
- pay_order_id: 支付网关订单号
- user_id: 用户ID
- username: 用户名
- amount: 充值金额
- coin_amount: 游戏币数量
- status: 订单状态 (0-待支付 1-成功 2-失败 3-取消)
- pay_way: 支付方式
- pay_time: 支付时间
- callback_data: 回调数据
- create_time: 创建时间
- update_time: 更新时间

## 测试流程

### 1. 正常充值流程

1. 访问 http://localhost:8080/recharge
2. 选择充值金额或输入自定义金额
3. 点击"生成支付宝二维码"
4. 系统创建充值订单并调用支付网关
5. 支付成功后，支付网关回调游戏服务器
6. 游戏服务器增加用户游戏币余额

### 2. 测试支付回调

```bash
# 模拟支付成功回调
curl -X POST http://localhost:8080/api/payment/test-notify \
  -d "payOrderId=P202109081234567890&state=2"
```

### 3. 查询用户信息

```bash
curl http://localhost:8080/recharge/user/testuser
```

## 注意事项

1. **回调地址**: 确保支付网关能访问到 `http://localhost:8080/api/payment/notify`
2. **签名验证**: 系统会验证支付回调的签名，确保数据安全
3. **幂等性**: 支付回调具有幂等性，重复回调不会重复加币
4. **事务处理**: 充值过程使用数据库事务，确保数据一致性

## 扩展功能

- 支持多种支付方式 (微信支付、银联等)
- 充值优惠活动 (首充双倍、充值返利等)
- VIP 等级系统
- 充值统计报表
- 风控系统

## 故障排除

### 常见问题

1. **支付回调失败**: 检查网络连通性和签名配置
2. **订单状态异常**: 查看日志确认回调数据格式
3. **游戏币未到账**: 检查数据库事务和用户ID匹配

### 日志查看

```bash
# 查看应用日志
tail -f logs/game-server.log

# 查看支付相关日志
grep "payment\|recharge" logs/game-server.log
```
